-- Supabase seed file for WhereZ application
-- This file contains initial data and setup for the application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- <PERSON><PERSON> enum types
CREATE TYPE oauth_provider AS ENUM ('apple', 'google', 'microsoft');
CREATE TYPE question_type AS ENUM ('text', 'audio', 'image');
CREATE TYPE question_status AS ENUM ('pending', 'processing', 'completed', 'failed');
CREATE TYPE token_type AS ENUM ('access', 'refresh');
CREATE TYPE content_type AS ENUM ('text', 'image', 'audio', 'document');
CREATE TYPE processing_status AS ENUM ('pending', 'processing', 'completed', 'failed');

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('images', 'images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
  ('thumbnails', 'thumbnails', true, 10485760, ARRAY['image/jpeg', 'image/png']),
  ('audio', 'audio', false, 52428800, ARRAY['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/aac']);

-- Create storage policies
CREATE POLICY "Users can upload their own images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own images" ON storage.objects
  FOR SELECT USING (bucket_id = 'images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own images" ON storage.objects
  FOR DELETE USING (bucket_id = 'images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can upload their own thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'thumbnails' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own thumbnails" ON storage.objects
  FOR SELECT USING (bucket_id = 'thumbnails' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own thumbnails" ON storage.objects
  FOR DELETE USING (bucket_id = 'thumbnails' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can upload their own audio" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'audio' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own audio" ON storage.objects
  FOR SELECT USING (bucket_id = 'audio' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own audio" ON storage.objects
  FOR DELETE USING (bucket_id = 'audio' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Create application tables
-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    avatar_url TEXT,
    provider oauth_provider NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider, provider_id)
);

-- Images table
CREATE TABLE IF NOT EXISTS public.images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    thumbnail_path TEXT,
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    width INTEGER,
    height INTEGER,
    description TEXT,
    llm_description TEXT,
    llm_tags TEXT[],
    processing_status processing_status DEFAULT 'pending',
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Questions table
CREATE TABLE IF NOT EXISTS public.questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    type question_type NOT NULL DEFAULT 'text',
    audio_filename VARCHAR(255),
    audio_file_path TEXT,
    audio_url TEXT,
    status question_status NOT NULL DEFAULT 'pending',
    llm_processed_content TEXT,
    context_summary TEXT,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Answers table
CREATE TABLE IF NOT EXISTS public.answers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    confidence FLOAT,
    sources TEXT[],
    llm_model VARCHAR(100),
    reasoning TEXT,
    question_id UUID NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Auth tokens table (for custom JWT implementation)
CREATE TABLE IF NOT EXISTS public.auth_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token TEXT NOT NULL UNIQUE,
    token_type token_type NOT NULL DEFAULT 'access',
    jti VARCHAR(255) NOT NULL UNIQUE,
    is_revoked CHAR(1) NOT NULL DEFAULT '0',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content embeddings table
CREATE TABLE IF NOT EXISTS public.content_embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID NOT NULL,
    content_type content_type NOT NULL,
    content_text TEXT NOT NULL,
    embedding vector(1536),
    metadata JSONB,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Processed content table for storing LLM analysis results
CREATE TABLE IF NOT EXISTS public.processed_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID NOT NULL,
    content_type content_type NOT NULL,
    original_content TEXT,
    processed_content TEXT NOT NULL,
    summary TEXT,
    key_points TEXT[],
    tags TEXT[],
    sentiment VARCHAR(50),
    confidence FLOAT,
    llm_model VARCHAR(100),
    processing_metadata JSONB,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction tables
CREATE TABLE IF NOT EXISTS public.question_image_context (
    question_id UUID REFERENCES public.questions(id) ON DELETE CASCADE,
    image_id UUID REFERENCES public.images(id) ON DELETE CASCADE,
    PRIMARY KEY (question_id, image_id)
);

CREATE TABLE IF NOT EXISTS public.answer_images (
    answer_id UUID REFERENCES public.answers(id) ON DELETE CASCADE,
    image_id UUID REFERENCES public.images(id) ON DELETE CASCADE,
    PRIMARY KEY (answer_id, image_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_provider ON public.users(provider, provider_id);
CREATE INDEX IF NOT EXISTS idx_images_user_id ON public.images(user_id);
CREATE INDEX IF NOT EXISTS idx_images_processing_status ON public.images(processing_status);
CREATE INDEX IF NOT EXISTS idx_questions_user_id ON public.questions(user_id);
CREATE INDEX IF NOT EXISTS idx_questions_status ON public.questions(status);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_user_id ON public.auth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_jti ON public.auth_tokens(jti);

-- Indexes for new tables
CREATE INDEX IF NOT EXISTS idx_content_embeddings_user_id ON public.content_embeddings(user_id);
CREATE INDEX IF NOT EXISTS idx_content_embeddings_content_type ON public.content_embeddings(content_type);
CREATE INDEX IF NOT EXISTS idx_content_embeddings_content_id ON public.content_embeddings(content_id);
CREATE INDEX IF NOT EXISTS idx_processed_content_user_id ON public.processed_content(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_content_content_type ON public.processed_content(content_type);
CREATE INDEX IF NOT EXISTS idx_processed_content_content_id ON public.processed_content(content_id);

-- Vector similarity search index
CREATE INDEX IF NOT EXISTS idx_content_embeddings_vector ON public.content_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.auth_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.question_image_context ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.answer_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processed_content ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can view and update their own profile
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.users FOR INSERT WITH CHECK (auth.uid() = id);

-- Users can manage their own images
CREATE POLICY "Users can view own images" ON public.images FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own images" ON public.images FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own images" ON public.images FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own images" ON public.images FOR DELETE USING (auth.uid() = user_id);

-- Users can manage their own questions
CREATE POLICY "Users can view own questions" ON public.questions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own questions" ON public.questions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own questions" ON public.questions FOR UPDATE USING (auth.uid() = user_id);

-- Users can view answers to their own questions
CREATE POLICY "Users can view answers to own questions" ON public.answers FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.questions WHERE questions.id = answers.question_id AND auth.uid() = questions.user_id)
);

-- Users can manage their own auth tokens
CREATE POLICY "Users can view own tokens" ON public.auth_tokens FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own tokens" ON public.auth_tokens FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own tokens" ON public.auth_tokens FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own tokens" ON public.auth_tokens FOR DELETE USING (auth.uid() = user_id);

-- Junction table policies
CREATE POLICY "Users can view own question image context" ON public.question_image_context FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.questions WHERE questions.id = question_image_context.question_id AND auth.uid() = questions.user_id)
);
CREATE POLICY "Users can insert own question image context" ON public.question_image_context FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM public.questions WHERE questions.id = question_image_context.question_id AND auth.uid() = questions.user_id)
);

CREATE POLICY "Users can view own answer images" ON public.answer_images FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.answers
        JOIN public.questions ON questions.id = answers.question_id
        WHERE answers.id = answer_images.answer_id AND auth.uid() = questions.user_id
    )
);

-- Content embeddings policies
CREATE POLICY "Users can view own content embeddings" ON public.content_embeddings FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own content embeddings" ON public.content_embeddings FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own content embeddings" ON public.content_embeddings FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own content embeddings" ON public.content_embeddings FOR DELETE USING (auth.uid() = user_id);

-- Processed content policies
CREATE POLICY "Users can view own processed content" ON public.processed_content FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own processed content" ON public.processed_content FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own processed content" ON public.processed_content FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own processed content" ON public.processed_content FOR DELETE USING (auth.uid() = user_id);

-- Function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, first_name, last_name, avatar_url, provider, provider_id)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name'),
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'given_name'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', NEW.raw_user_meta_data->>'family_name'),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', NEW.raw_user_meta_data->>'picture'),
        COALESCE(NEW.app_metadata->>'provider', 'email')::oauth_provider,
        COALESCE(NEW.raw_user_meta_data->>'sub', NEW.id::text)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_images_updated_at BEFORE UPDATE ON public.images
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_questions_updated_at BEFORE UPDATE ON public.questions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_answers_updated_at BEFORE UPDATE ON public.answers
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_auth_tokens_updated_at BEFORE UPDATE ON public.auth_tokens
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_content_embeddings_updated_at BEFORE UPDATE ON public.content_embeddings
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_processed_content_updated_at BEFORE UPDATE ON public.processed_content
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
