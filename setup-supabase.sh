#!/bin/bash

# WhereZ Supabase Setup Script
# This script helps you set up the WhereZ application with Supabase

set -e

echo "🚀 WhereZ Supabase Setup"
echo "========================"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "📦 Installing Supabase CLI..."
    npm install -g supabase
else
    echo "✅ Supabase CLI is already installed"
fi

# Check if we're in the right directory
if [ ! -f "package.json" ] && [ ! -f "frontend/package.json" ]; then
    echo "❌ Please run this script from the WhereZ project root directory"
    exit 1
fi

echo ""
echo "🔧 Setting up Supabase project..."

# Initialize Supabase if not already done
if [ ! -f "supabase/config.toml" ]; then
    echo "📝 Initializing Supabase..."
    supabase init
else
    echo "✅ Supabase already initialized"
fi

# Prompt for project reference
echo ""
echo "🔗 Linking to Supabase project..."
echo "Please enter your Supabase project reference ID:"
echo "(You can find this in your Supabase dashboard URL: https://app.supabase.com/project/YOUR-PROJECT-REF)"
read -p "Project Reference ID: " PROJECT_REF

if [ -z "$PROJECT_REF" ]; then
    echo "❌ Project reference ID is required"
    exit 1
fi

# Link to Supabase project
echo "🔗 Linking to project: $PROJECT_REF"
supabase link --project-ref "$PROJECT_REF"

# Push database schema
echo ""
echo "📊 Setting up database schema..."
supabase db push

echo ""
echo "🎉 Supabase setup complete!"
echo ""
echo "Next steps:"
echo "1. Go to your Supabase dashboard: https://app.supabase.com/project/$PROJECT_REF"
echo "2. Copy your project URL and anon key from Settings > API"
echo "3. Configure OAuth providers in Authentication > Providers"
echo "4. Update your .env files with Supabase credentials"
echo "5. Run the frontend: cd frontend && npm install && npx expo start"
echo ""
echo "📚 For detailed setup instructions, see: SUPABASE_DEPLOYMENT.md"
echo ""
echo "🔑 Don't forget to:"
echo "   - Set up OAuth providers (Google, Apple, Microsoft)"
echo "   - Configure redirect URLs for your app"
echo "   - Update environment variables in both backend and frontend"
echo ""
echo "Happy coding! 🎯"
