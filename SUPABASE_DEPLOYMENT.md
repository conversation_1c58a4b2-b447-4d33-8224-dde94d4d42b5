# WhereZ - Supabase Deployment Guide

This guide will help you deploy the WhereZ application using Supabase as the backend infrastructure.

## 🚀 Quick Start with Supabase

### Prerequisites

- Node.js 18+ and npm/yarn
- Supabase CLI: `npm install -g supabase`
- Git
- A Supabase account (free tier available)

### 1. Create Supabase Project

1. **Sign up for Supabase**: Go to [supabase.com](https://supabase.com) and create an account
2. **Create a new project**: 
   - Click "New Project"
   - Choose your organization
   - Enter project name: "wheresz"
   - Set a strong database password
   - Choose a region close to your users
   - Click "Create new project"

3. **Get your project credentials**:
   - Go to Settings > API
   - Copy your Project URL and anon public key
   - Note your Project Reference ID

### 2. Set Up Local Development

1. **Clone and setup the project**:
   ```bash
   git clone <your-repo>
   cd wheresz
   ```

2. **Initialize Supabase locally**:
   ```bash
   supabase init
   supabase login
   supabase link --project-ref <your-project-ref>
   ```

3. **Set up environment variables**:

   **Backend (.env)**:
   ```env
   # Supabase Configuration
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   USE_SUPABASE=true
   
   # Database (Supabase PostgreSQL)
   DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
   DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:[YOUR-PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
   
   # OAuth Configuration (configure in Supabase Dashboard)
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   APPLE_CLIENT_ID=your-apple-client-id
   APPLE_SECRET=your-apple-secret
   MICROSOFT_CLIENT_ID=your-microsoft-client-id
   MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
   ```

   **Frontend (.env)**:
   ```env
   # Supabase Configuration
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   USE_SUPABASE=true
   
   # API Configuration (optional - for hybrid mode)
   API_BASE_URL=https://your-backend-url.com
   ```

### 3. Database Setup

1. **Run migrations**:
   ```bash
   supabase db push
   ```

2. **Seed the database** (optional):
   ```bash
   supabase db reset
   ```

3. **Verify setup**:
   ```bash
   supabase status
   ```

### 4. Configure Authentication

1. **Go to Supabase Dashboard** > Authentication > Providers

2. **Enable OAuth providers**:

   **Google OAuth**:
   - Enable Google provider
   - Add your Google Client ID and Secret
   - Add redirect URL: `https://your-project-ref.supabase.co/auth/v1/callback`
   - For mobile: Add `wheresz://auth/callback`

   **Apple OAuth**:
   - Enable Apple provider
   - Add your Apple Client ID and Secret
   - Configure redirect URLs

   **Microsoft OAuth**:
   - Enable Azure (Microsoft) provider
   - Add your Microsoft Client ID and Secret
   - Configure redirect URLs

3. **Configure redirect URLs**:
   - Go to Authentication > URL Configuration
   - Add your app URLs:
     - `wheresz://auth/callback` (mobile)
     - `http://localhost:3000` (web dev)
     - `https://your-domain.com` (production)

### 5. Storage Setup

1. **Create storage buckets** (automatically created by seed script):
   - `images` - for user uploaded images
   - `thumbnails` - for image thumbnails
   - `audio` - for audio recordings

2. **Configure storage policies** (already included in migration):
   - Users can only access their own files
   - Public read access for images and thumbnails
   - Private access for audio files

### 6. Deploy Backend (Optional - Hybrid Mode)

If you want to use both Supabase and your custom backend:

1. **Deploy to your preferred platform**:
   - Vercel, Netlify, Railway, Render, etc.
   - Set environment variables in your deployment platform
   - Ensure `USE_SUPABASE=true` is set

2. **Update frontend API URL**:
   ```env
   API_BASE_URL=https://your-deployed-backend.com
   ```

### 7. Deploy Frontend

#### Option A: Expo Application Services (EAS)

1. **Install EAS CLI**:
   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Configure EAS**:
   ```bash
   cd frontend
   eas build:configure
   ```

3. **Update app.json** with your Supabase config:
   ```json
   {
     "expo": {
       "extra": {
         "supabaseUrl": "https://your-project-ref.supabase.co",
         "supabaseAnonKey": "your-anon-key"
       }
     }
   }
   ```

4. **Build and deploy**:
   ```bash
   # Build for app stores
   eas build --platform all
   
   # Submit to stores
   eas submit --platform ios
   eas submit --platform android
   ```

#### Option B: Expo Web

1. **Build for web**:
   ```bash
   cd frontend
   npx expo export:web
   ```

2. **Deploy to Vercel/Netlify**:
   ```bash
   # Vercel
   npx vercel --prod
   
   # Netlify
   npx netlify deploy --prod --dir=web-build
   ```

## 🔧 Configuration Details

### Environment Variables

#### Required Supabase Variables

```env
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### OAuth Provider Configuration

**Google**:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 credentials
3. Add authorized redirect URIs:
   - `https://your-project-ref.supabase.co/auth/v1/callback`
   - `wheresz://auth/callback`

**Apple**:
1. Go to [Apple Developer Portal](https://developer.apple.com/)
2. Create App ID and Service ID
3. Configure Sign In with Apple
4. Generate private key

**Microsoft**:
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register application in Azure AD
3. Configure authentication platform
4. Add redirect URIs

### Database Schema

The application uses the following main tables:

- `auth.users` - Supabase built-in user authentication
- `public.users` - Extended user profiles
- `public.images` - Image metadata and storage references
- `public.questions` - User questions
- `public.answers` - Generated answers
- `public.auth_tokens` - Custom JWT tokens (if using hybrid mode)

### Storage Buckets

- **images**: Public bucket for user images (max 50MB per file)
- **thumbnails**: Public bucket for image thumbnails
- **audio**: Private bucket for audio recordings

### Row Level Security (RLS)

All tables have RLS enabled with policies ensuring:
- Users can only access their own data
- Proper authentication is required
- Data isolation between users

## 🚀 Production Deployment

### 1. Supabase Production Setup

1. **Upgrade to Pro plan** (if needed for production features)
2. **Configure custom domain** (optional)
3. **Set up database backups**
4. **Configure monitoring and alerts**

### 2. Security Checklist

- [ ] Enable RLS on all tables
- [ ] Configure proper OAuth redirect URLs
- [ ] Set strong database password
- [ ] Enable 2FA on Supabase account
- [ ] Review and test all security policies
- [ ] Configure rate limiting (Supabase Pro)
- [ ] Set up monitoring and logging

### 3. Performance Optimization

- [ ] Add database indexes for frequently queried columns
- [ ] Configure CDN for static assets
- [ ] Enable database connection pooling
- [ ] Set up caching strategies
- [ ] Monitor query performance

### 4. Monitoring and Maintenance

1. **Set up monitoring**:
   - Supabase Dashboard metrics
   - Custom application monitoring
   - Error tracking (Sentry, etc.)

2. **Regular maintenance**:
   - Database backups verification
   - Security updates
   - Performance monitoring
   - User feedback analysis

## 🔄 Migration from Custom Backend

If you're migrating from the custom FastAPI backend:

1. **Data migration**:
   ```bash
   # Export data from old database
   pg_dump old_database > backup.sql
   
   # Import to Supabase (modify as needed)
   psql "postgresql://postgres:[PASSWORD]@db.your-project-ref.supabase.co:5432/postgres" < backup.sql
   ```

2. **Update application configuration**:
   - Set `USE_SUPABASE=true`
   - Update environment variables
   - Test authentication flows
   - Verify data access

3. **Gradual migration**:
   - Start with authentication
   - Migrate file storage
   - Move database operations
   - Update API calls

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Auth with React Native](https://supabase.com/docs/guides/auth/auth-helpers/react-native)
- [Expo and Supabase Guide](https://docs.expo.dev/guides/using-supabase/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase Storage Guide](https://supabase.com/docs/guides/storage)

## 🆘 Troubleshooting

### Common Issues

1. **Authentication not working**:
   - Check OAuth provider configuration
   - Verify redirect URLs
   - Check Supabase Auth settings

2. **Database connection issues**:
   - Verify database URL and password
   - Check network connectivity
   - Review connection pooling settings

3. **File upload failures**:
   - Check storage bucket policies
   - Verify file size limits
   - Review CORS settings

4. **RLS policy errors**:
   - Review policy definitions
   - Check user authentication
   - Test with different user roles

### Getting Help

- [Supabase Discord Community](https://discord.supabase.com/)
- [Supabase GitHub Discussions](https://github.com/supabase/supabase/discussions)
- [Stack Overflow - Supabase Tag](https://stackoverflow.com/questions/tagged/supabase)

## 🎉 Success!

Once deployed, your WhereZ application will be running on Supabase with:

- ✅ Scalable PostgreSQL database
- ✅ Built-in authentication with OAuth providers
- ✅ File storage with CDN
- ✅ Real-time subscriptions
- ✅ Row-level security
- ✅ Automatic backups
- ✅ Global edge network

Your app is now ready for production use with enterprise-grade infrastructure!
