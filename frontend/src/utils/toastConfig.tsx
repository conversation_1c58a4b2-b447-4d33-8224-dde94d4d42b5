import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '@/constants';

export const toastConfig = {
  success: ({ text1, text2 }: any) => (
    <View style={[styles.container, styles.success]}>
      <Ionicons name="checkmark-circle" size={24} color={COLORS.success} />
      <View style={styles.textContainer}>
        <Text style={[styles.title, styles.successText]}>{text1}</Text>
        {text2 && <Text style={[styles.subtitle, styles.successText]}>{text2}</Text>}
      </View>
    </View>
  ),
  
  error: ({ text1, text2 }: any) => (
    <View style={[styles.container, styles.error]}>
      <Ionicons name="alert-circle" size={24} color={COLORS.error} />
      <View style={styles.textContainer}>
        <Text style={[styles.title, styles.errorText]}>{text1}</Text>
        {text2 && <Text style={[styles.subtitle, styles.errorText]}>{text2}</Text>}
      </View>
    </View>
  ),
  
  info: ({ text1, text2 }: any) => (
    <View style={[styles.container, styles.info]}>
      <Ionicons name="information-circle" size={24} color={COLORS.info} />
      <View style={styles.textContainer}>
        <Text style={[styles.title, styles.infoText]}>{text1}</Text>
        {text2 && <Text style={[styles.subtitle, styles.infoText]}>{text2}</Text>}
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    marginHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  success: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.success,
  },
  
  error: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.error,
  },
  
  info: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.info,
  },
  
  textContainer: {
    flex: 1,
    marginLeft: SPACING.sm,
  },
  
  title: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    marginBottom: 2,
  },
  
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.sm,
    opacity: 0.8,
  },
  
  successText: {
    color: COLORS.success,
  },
  
  errorText: {
    color: COLORS.error,
  },
  
  infoText: {
    color: COLORS.info,
  },
});
