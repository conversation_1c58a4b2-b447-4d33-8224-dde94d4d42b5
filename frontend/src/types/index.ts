// API Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  timestamp?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// User Types
export interface User {
  id: string;
  email: string;
  name?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  provider: 'apple' | 'google' | 'microsoft';
  created_at: string;
  updated_at: string;
}

export interface UpdateUserProfile {
  name?: string;
  first_name?: string;
  last_name?: string;
}

// Auth Types
export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface AuthResponse extends AuthTokens {
  user: User;
}

export interface AppleAuthRequest {
  identity_token: string;
  authorization_code: string;
  user_info?: {
    name?: {
      firstName?: string;
      lastName?: string;
    };
    email?: string;
  };
}

export interface GoogleAuthRequest {
  id_token: string;
  access_token?: string;
}

export interface MicrosoftAuthRequest {
  access_token: string;
  id_token?: string;
}

// Image Types
export interface ImageData {
  id: string;
  filename: string;
  url: string;
  thumbnail_url?: string;
  size: number;
  mime_type: string;
  width?: number;
  height?: number;
  description?: string;
  created_at: string;
  user_id: string;
}

export interface ImageListResponse {
  images: ImageData[];
  pagination: PaginationInfo;
}

// Question Types
export interface Question {
  id: string;
  content: string;
  type: 'text' | 'audio';
  audio_url?: string;
  context_images: ImageData[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  user_id: string;
}

export interface QuestionRequest {
  content: string;
  type: 'text' | 'audio';
  context_images?: string[];
}

export interface QuestionListResponse {
  questions: Question[];
  pagination: PaginationInfo;
}

// Answer Types
export interface Answer {
  id: string;
  content: string;
  images: ImageData[];
  confidence?: number;
  sources: string[];
  created_at: string;
  question_id: string;
}

export interface QuestionWithAnswer extends Question {
  answer?: Answer;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Questions: undefined;
  Images: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeScreen: undefined;
  AskQuestion: undefined;
  QuestionDetail: { questionId: string };
};

export type QuestionsStackParamList = {
  QuestionsList: undefined;
  QuestionDetail: { questionId: string };
};

export type ImagesStackParamList = {
  ImagesList: undefined;
  ImageDetail: { imageId: string };
  ImageUpload: undefined;
};

export type ProfileStackParamList = {
  ProfileScreen: undefined;
  EditProfile: undefined;
};

// Component Props Types
export interface LoadingProps {
  size?: 'small' | 'large';
  color?: string;
}

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  secureTextEntry?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  error?: string;
  disabled?: boolean;
}

// Error Types
export interface ApiError {
  error: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Storage Types
export interface StorageKeys {
  ACCESS_TOKEN: string;
  REFRESH_TOKEN: string;
  USER_DATA: string;
  SETTINGS: string;
}

// Settings Types
export interface AppSettings {
  notifications_enabled: boolean;
  auto_upload_images: boolean;
  audio_quality: 'low' | 'medium' | 'high';
  theme: 'light' | 'dark' | 'auto';
}
