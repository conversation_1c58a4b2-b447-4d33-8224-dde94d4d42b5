import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Animated,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import { useAuth } from '@/context/AuthContext';
import { apiClient } from '@/services/apiClient';

interface EnhancedQuestionInputProps {
  onQuestionSubmitted?: (questionId: string) => void;
  placeholder?: string;
  showAudioInput?: boolean;
  showImageContext?: boolean;
}

export const EnhancedQuestionInput: React.FC<EnhancedQuestionInputProps> = ({
  onQuestionSubmitted,
  placeholder = "Ask me anything about your content...",
  showAudioInput = true,
  showImageContext = true,
}) => {
  const [question, setQuestion] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  
  const { user } = useAuth();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const inputRef = useRef<TextInput>(null);

  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant microphone permission to record audio questions.');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(recording);
      setIsRecording(true);
      
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
      
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Recording Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    setIsRecording(false);
    pulseAnim.stopAnimation();
    pulseAnim.setValue(1);
    
    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (uri) {
        await submitAudioQuestion(uri);
      }
      
      setRecording(null);
    } catch (err) {
      console.error('Failed to stop recording', err);
      Alert.alert('Recording Error', 'Failed to process recording. Please try again.');
    }
  };

  const submitAudioQuestion = async (audioUri: string) => {
    setIsSubmitting(true);
    
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        type: 'audio/m4a',
        name: 'question.m4a',
      } as any);
      formData.append('content', 'Audio question');

      const response = await apiClient.post('/questions/audio', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (onQuestionSubmitted) {
        onQuestionSubmitted(response.data.id);
      }
      
      Alert.alert('Success', 'Your audio question has been submitted!');
    } catch (error) {
      console.error('Error submitting audio question:', error);
      Alert.alert('Error', 'Failed to submit audio question. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitTextQuestion = async () => {
    if (!question.trim()) return;

    setIsSubmitting(true);
    Keyboard.dismiss();
    
    try {
      const response = await apiClient.post('/questions', {
        content: question.trim(),
        type: 'text',
        image_context: selectedImages.length > 0 ? selectedImages : undefined,
      });

      if (onQuestionSubmitted) {
        onQuestionSubmitted(response.data.id);
      }
      
      setQuestion('');
      setSelectedImages([]);
      Alert.alert('Success', 'Your question has been submitted!');
    } catch (error) {
      console.error('Error submitting question:', error);
      Alert.alert('Error', 'Failed to submit question. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets) {
        const imageIds = result.assets.map(asset => asset.uri);
        setSelectedImages(prev => [...prev, ...imageIds].slice(0, 5)); // Limit to 5 images
      }
    } catch (error) {
      console.error('Error selecting images:', error);
      Alert.alert('Error', 'Failed to select images. Please try again.');
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const getSuggestions = () => [
    "What themes do you see in my content?",
    "Summarize my recent uploads",
    "What questions have I asked before?",
    "Find content similar to...",
    "What insights can you provide?",
  ];

  const handleSuggestionPress = (suggestion: string) => {
    setQuestion(suggestion);
    inputRef.current?.focus();
  };

  return (
    <View style={styles.container}>
      {/* Question Suggestions */}
      {question.length === 0 && (
        <View style={styles.suggestionsContainer}>
          <Text style={styles.suggestionsTitle}>Try asking:</Text>
          <View style={styles.suggestionsList}>
            {getSuggestions().map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionChip}
                onPress={() => handleSuggestionPress(suggestion)}
              >
                <Text style={styles.suggestionText}>{suggestion}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Selected Images */}
      {selectedImages.length > 0 && (
        <View style={styles.selectedImagesContainer}>
          <Text style={styles.selectedImagesTitle}>Context Images:</Text>
          <View style={styles.selectedImagesList}>
            {selectedImages.map((imageUri, index) => (
              <View key={index} style={styles.selectedImageItem}>
                <Ionicons name="image" size={16} color="#007bff" />
                <Text style={styles.selectedImageText}>Image {index + 1}</Text>
                <TouchableOpacity onPress={() => removeImage(index)}>
                  <Ionicons name="close-circle" size={16} color="#dc3545" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Main Input Area */}
      <View style={styles.inputContainer}>
        <View style={styles.textInputContainer}>
          <TextInput
            ref={inputRef}
            style={styles.textInput}
            value={question}
            onChangeText={setQuestion}
            placeholder={placeholder}
            placeholderTextColor="#999"
            multiline
            maxLength={1000}
            editable={!isSubmitting && !isRecording}
          />
          
          {/* Input Actions */}
          <View style={styles.inputActions}>
            {showImageContext && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={selectImages}
                disabled={isSubmitting || isRecording}
              >
                <Ionicons 
                  name="images-outline" 
                  size={20} 
                  color={selectedImages.length > 0 ? "#007bff" : "#666"} 
                />
              </TouchableOpacity>
            )}
            
            {showAudioInput && (
              <TouchableOpacity
                style={[styles.actionButton, isRecording && styles.recordingButton]}
                onPress={isRecording ? stopRecording : startRecording}
                disabled={isSubmitting}
              >
                <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                  <Ionicons 
                    name={isRecording ? "stop" : "mic-outline"} 
                    size={20} 
                    color={isRecording ? "#dc3545" : "#666"} 
                  />
                </Animated.View>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                (!question.trim() || isSubmitting || isRecording) && styles.submitButtonDisabled
              ]}
              onPress={submitTextQuestion}
              disabled={!question.trim() || isSubmitting || isRecording}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Ionicons name="send" size={20} color="#fff" />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Recording Status */}
      {isRecording && (
        <View style={styles.recordingStatus}>
          <View style={styles.recordingIndicator}>
            <View style={styles.recordingDot} />
            <Text style={styles.recordingText}>Recording... Tap to stop</Text>
          </View>
        </View>
      )}

      {/* Character Count */}
      {question.length > 0 && (
        <Text style={styles.characterCount}>
          {question.length}/1000
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  suggestionsContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  suggestionsTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    fontWeight: '600',
  },
  suggestionsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  suggestionChip: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  suggestionText: {
    fontSize: 12,
    color: '#495057',
  },
  selectedImagesContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  selectedImagesTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    fontWeight: '600',
  },
  selectedImagesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedImageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  selectedImageText: {
    fontSize: 12,
    color: '#007bff',
    marginHorizontal: 4,
  },
  inputContainer: {
    padding: 16,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 44,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    maxHeight: 100,
    paddingVertical: 8,
  },
  inputActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  actionButton: {
    padding: 8,
    marginHorizontal: 2,
  },
  recordingButton: {
    backgroundColor: '#ffe6e6',
    borderRadius: 16,
  },
  submitButton: {
    backgroundColor: '#007bff',
    padding: 8,
    borderRadius: 16,
    marginLeft: 4,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  recordingStatus: {
    backgroundColor: '#fff3cd',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#ffeaa7',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#dc3545',
    marginRight: 8,
  },
  recordingText: {
    fontSize: 14,
    color: '#856404',
    fontWeight: '600',
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
});
