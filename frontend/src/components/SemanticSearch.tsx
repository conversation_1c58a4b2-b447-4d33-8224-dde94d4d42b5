import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@/context/AuthContext';
import { apiClient } from '@/services/apiClient';

interface SearchResult {
  content_id: string;
  content_type: string;
  content_text: string;
  similarity_score: number;
  metadata: Record<string, any>;
  created_at: string;
}

interface SemanticSearchResponse {
  query: string;
  results: SearchResult[];
  total_results: number;
}

interface SemanticSearchProps {
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  contentTypes?: string[];
}

export const SemanticSearch: React.FC<SemanticSearchProps> = ({
  onResultSelect,
  placeholder = "Search your content...",
  contentTypes,
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const { user } = useAuth();

  const performSearch = useCallback(async () => {
    if (!query.trim() || !user) return;

    setLoading(true);
    try {
      const response = await apiClient.post<SemanticSearchResponse>('/search/semantic', {
        query: query.trim(),
        content_types: contentTypes,
        limit: 20,
        similarity_threshold: 0.6,
      });

      setResults(response.data.results);
      setHasSearched(true);
    } catch (error) {
      console.error('Search error:', error);
      Alert.alert('Search Error', 'Failed to search content. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [query, user, contentTypes]);

  const handleResultPress = useCallback((result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
  }, [onResultSelect]);

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType) {
      case 'image':
        return 'image-outline';
      case 'audio':
        return 'musical-notes-outline';
      case 'text':
        return 'document-text-outline';
      default:
        return 'document-outline';
    }
  };

  const formatSimilarityScore = (score: number) => {
    return `${Math.round(score * 100)}% match`;
  };

  const truncateText = (text: string, maxLength: number = 150) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      style={styles.resultItem}
      onPress={() => handleResultPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.resultHeader}>
        <View style={styles.contentTypeContainer}>
          <Ionicons
            name={getContentTypeIcon(item.content_type) as any}
            size={16}
            color="#666"
          />
          <Text style={styles.contentType}>{item.content_type}</Text>
        </View>
        <Text style={styles.similarityScore}>
          {formatSimilarityScore(item.similarity_score)}
        </Text>
      </View>
      
      <Text style={styles.resultText}>
        {truncateText(item.content_text)}
      </Text>
      
      {item.metadata && Object.keys(item.metadata).length > 0 && (
        <View style={styles.metadataContainer}>
          {item.metadata.tags && (
            <View style={styles.tagsContainer}>
              {item.metadata.tags.slice(0, 3).map((tag: string, index: number) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      )}
      
      <Text style={styles.timestamp}>
        {new Date(item.created_at).toLocaleDateString()}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            value={query}
            onChangeText={setQuery}
            placeholder={placeholder}
            placeholderTextColor="#999"
            onSubmitEditing={performSearch}
            returnKeyType="search"
            editable={!loading}
          />
          {query.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setQuery('');
                setResults([]);
                setHasSearched(false);
              }}
              style={styles.clearButton}
            >
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity
          style={[styles.searchButton, loading && styles.searchButtonDisabled]}
          onPress={performSearch}
          disabled={loading || !query.trim()}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.searchButtonText}>Search</Text>
          )}
        </TouchableOpacity>
      </View>

      {hasSearched && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsHeader}>
            {results.length > 0
              ? `Found ${results.length} result${results.length === 1 ? '' : 's'}`
              : 'No results found'
            }
          </Text>
          
          <FlatList
            data={results}
            renderItem={renderSearchResult}
            keyExtractor={(item) => `${item.content_type}-${item.content_id}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.resultsList}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="search-outline" size={48} color="#ccc" />
                <Text style={styles.emptyText}>
                  No content matches your search query.
                </Text>
                <Text style={styles.emptySubtext}>
                  Try using different keywords or upload more content.
                </Text>
              </View>
            }
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  searchButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
  },
  searchButtonDisabled: {
    backgroundColor: '#ccc',
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  resultsHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  resultsList: {
    paddingBottom: 20,
  },
  resultItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  contentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentType: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  similarityScore: {
    fontSize: 12,
    color: '#28a745',
    fontWeight: '600',
  },
  resultText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  metadataContainer: {
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#e9ecef',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#495057',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});
