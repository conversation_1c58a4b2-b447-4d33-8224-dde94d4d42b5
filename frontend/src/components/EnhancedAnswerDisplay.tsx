import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import { LinearGradient } from 'expo-linear-gradient';

interface Answer {
  id: string;
  content: string;
  confidence?: number;
  sources?: string[];
  llm_model?: string;
  reasoning?: string;
  created_at: string;
}

interface Question {
  id: string;
  content: string;
  type: string;
  created_at: string;
}

interface EnhancedAnswerDisplayProps {
  question: Question;
  answer: Answer;
  onRelatedContentPress?: () => void;
  onSharePress?: () => void;
}

export const EnhancedAnswerDisplay: React.FC<EnhancedAnswerDisplayProps> = ({
  question,
  answer,
  onRelatedContentPress,
  onSharePress,
}) => {
  const [showReasoning, setShowReasoning] = useState(false);
  const [showSources, setShowSources] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return '#6c757d';
    if (confidence >= 0.8) return '#28a745';
    if (confidence >= 0.6) return '#ffc107';
    return '#fd7e14';
  };

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return 'Unknown';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };

  const copyToClipboard = async () => {
    try {
      await Clipboard.setStringAsync(answer.content);
      // You might want to show a toast here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const shareAnswer = () => {
    if (onSharePress) {
      onSharePress();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const renderConfidenceBadge = () => {
    if (!answer.confidence) return null;

    const confidence = answer.confidence;
    const color = getConfidenceColor(confidence);
    const text = getConfidenceText(confidence);

    return (
      <View style={[styles.confidenceBadge, { backgroundColor: color }]}>
        <Ionicons name="checkmark-circle" size={12} color="#fff" />
        <Text style={styles.confidenceText}>{text} Confidence</Text>
        <Text style={styles.confidencePercentage}>
          {Math.round(confidence * 100)}%
        </Text>
      </View>
    );
  };

  const renderModelBadge = () => {
    if (!answer.llm_model) return null;

    return (
      <View style={styles.modelBadge}>
        <Ionicons name="hardware-chip-outline" size={12} color="#007bff" />
        <Text style={styles.modelText}>{answer.llm_model}</Text>
      </View>
    );
  };

  const renderSources = () => {
    if (!answer.sources || answer.sources.length === 0) return null;

    return (
      <View style={styles.sourcesContainer}>
        <TouchableOpacity
          style={styles.sourcesHeader}
          onPress={() => setShowSources(!showSources)}
        >
          <Ionicons name="library-outline" size={16} color="#666" />
          <Text style={styles.sourcesTitle}>
            Sources ({answer.sources.length})
          </Text>
          <Ionicons
            name={showSources ? "chevron-up" : "chevron-down"}
            size={16}
            color="#666"
          />
        </TouchableOpacity>
        
        {showSources && (
          <View style={styles.sourcesList}>
            {answer.sources.map((source, index) => (
              <View key={index} style={styles.sourceItem}>
                <View style={styles.sourceBullet} />
                <Text style={styles.sourceText}>{source}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderReasoning = () => {
    if (!answer.reasoning) return null;

    return (
      <View style={styles.reasoningContainer}>
        <TouchableOpacity
          style={styles.reasoningHeader}
          onPress={() => setShowReasoning(!showReasoning)}
        >
          <Ionicons name="bulb-outline" size={16} color="#666" />
          <Text style={styles.reasoningTitle}>How I arrived at this answer</Text>
          <Ionicons
            name={showReasoning ? "chevron-up" : "chevron-down"}
            size={16}
            color="#666"
          />
        </TouchableOpacity>
        
        {showReasoning && (
          <View style={styles.reasoningContent}>
            <Text style={styles.reasoningText}>{answer.reasoning}</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Question Display */}
      <View style={styles.questionContainer}>
        <View style={styles.questionHeader}>
          <Ionicons name="help-circle-outline" size={20} color="#007bff" />
          <Text style={styles.questionLabel}>Your Question</Text>
          <View style={styles.questionTypeContainer}>
            <Ionicons
              name={question.type === 'audio' ? 'mic' : 'text'}
              size={12}
              color="#666"
            />
            <Text style={styles.questionType}>{question.type}</Text>
          </View>
        </View>
        <Text style={styles.questionText}>{question.content}</Text>
        <Text style={styles.timestamp}>{formatTimestamp(question.created_at)}</Text>
      </View>

      {/* Answer Display */}
      <LinearGradient
        colors={['#f8f9fa', '#ffffff']}
        style={styles.answerContainer}
      >
        <View style={styles.answerHeader}>
          <View style={styles.answerHeaderLeft}>
            <Ionicons name="sparkles" size={20} color="#007bff" />
            <Text style={styles.answerLabel}>AI Answer</Text>
          </View>
          <View style={styles.answerHeaderRight}>
            {renderModelBadge()}
            {renderConfidenceBadge()}
          </View>
        </View>

        <ScrollView style={styles.answerContent} showsVerticalScrollIndicator={false}>
          <Text style={styles.answerText}>{answer.content}</Text>
        </ScrollView>

        {/* Answer Actions */}
        <View style={styles.answerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={copyToClipboard}>
            <Ionicons name="copy-outline" size={18} color="#666" />
            <Text style={styles.actionText}>Copy</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={shareAnswer}>
            <Ionicons name="share-outline" size={18} color="#666" />
            <Text style={styles.actionText}>Share</Text>
          </TouchableOpacity>
          
          {onRelatedContentPress && (
            <TouchableOpacity style={styles.actionButton} onPress={onRelatedContentPress}>
              <Ionicons name="link-outline" size={18} color="#666" />
              <Text style={styles.actionText}>Related</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Sources */}
        {renderSources()}

        {/* Reasoning */}
        {renderReasoning()}

        <Text style={styles.answerTimestamp}>
          Answered {formatTimestamp(answer.created_at)}
        </Text>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  questionContainer: {
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginLeft: 8,
    flex: 1,
  },
  questionTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e9ecef',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  questionType: {
    fontSize: 10,
    color: '#666',
    marginLeft: 4,
    textTransform: 'uppercase',
  },
  questionText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  answerContainer: {
    padding: 16,
  },
  answerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  answerHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  answerLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginLeft: 8,
  },
  answerHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confidenceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  confidenceText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '600',
    marginLeft: 4,
  },
  confidencePercentage: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '700',
    marginLeft: 4,
  },
  modelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  modelText: {
    fontSize: 10,
    color: '#007bff',
    fontWeight: '600',
    marginLeft: 4,
  },
  answerContent: {
    maxHeight: 300,
    marginBottom: 16,
  },
  answerText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  answerActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f1f3f4',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  actionText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  sourcesContainer: {
    marginBottom: 16,
  },
  sourcesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  sourcesTitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  sourcesList: {
    marginTop: 8,
    paddingLeft: 16,
  },
  sourceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  sourceBullet: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#007bff',
    marginTop: 8,
    marginRight: 12,
  },
  sourceText: {
    flex: 1,
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
  reasoningContainer: {
    marginBottom: 16,
  },
  reasoningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  reasoningTitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  reasoningContent: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  reasoningText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
    fontStyle: 'italic',
  },
  answerTimestamp: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});
