import React, { createContext, useContext, useEffect, useReducer, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';

import { authService } from '@/services/auth';
import { STORAGE_KEYS } from '@/constants';
import type { User, AuthResponse } from '@/types';

// Auth State
interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user: User | null;
  error: string | null;
}

// Auth Actions
type AuthAction =
  | { type: 'LOADING' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User } }
  | { type: 'LOGIN_ERROR'; payload: { error: string } }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: { user: User } };

// Auth Context
interface AuthContextType extends AuthState {
  signInWithApple: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithMicrosoft: () => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  updateUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOADING':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        error: null,
      };

    case 'LOGIN_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: action.payload.error,
      };

    case 'LOGOUT':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload.user,
      };

    default:
      return state;
  }
}

// Initial State
const initialState: AuthState = {
  isLoading: true,
  isAuthenticated: false,
  user: null,
  error: null,
};

// Auth Provider
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing authentication on app start
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      dispatch({ type: 'LOADING' });

      const [accessToken, userData] = await Promise.all([
        SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN),
        SecureStore.getItemAsync(STORAGE_KEYS.USER_DATA),
      ]);

      if (accessToken && userData) {
        const user: User = JSON.parse(userData);
        dispatch({ type: 'LOGIN_SUCCESS', payload: { user } });
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({ type: 'LOGOUT' });
    }
  };

  const storeAuthData = async (authResponse: AuthResponse) => {
    await Promise.all([
      SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, authResponse.access_token),
      SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refresh_token),
      SecureStore.setItemAsync(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user)),
    ]);
  };

  const clearAuthData = async () => {
    await Promise.all([
      SecureStore.deleteItemAsync(STORAGE_KEYS.ACCESS_TOKEN),
      SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN),
      SecureStore.deleteItemAsync(STORAGE_KEYS.USER_DATA),
    ]);
  };

  const signInWithApple = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithApple();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const signInWithGoogle = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithGoogle();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const signInWithMicrosoft = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithMicrosoft();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const logout = async () => {
    try {
      dispatch({ type: 'LOADING' });
      await authService.logout();
      await clearAuthData();
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      // Even if logout fails, clear local data
      await clearAuthData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const updateUser = (user: User) => {
    dispatch({ type: 'UPDATE_USER', payload: { user } });
    // Update stored user data
    SecureStore.setItemAsync(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
  };

  const contextValue: AuthContextType = {
    ...state,
    signInWithApple,
    signInWithGoogle,
    signInWithMicrosoft,
    logout,
    clearError,
    updateUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Auth Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
