import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { SemanticSearch } from '@/components/SemanticSearch';
import { ContentInsights } from '@/components/ContentInsights';
import { EnhancedQuestionInput } from '@/components/EnhancedQuestionInput';

type TabType = 'search' | 'insights' | 'ask';

interface SearchResult {
  content_id: string;
  content_type: string;
  content_text: string;
  similarity_score: number;
  metadata: Record<string, any>;
  created_at: string;
}

export const SearchScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('search');
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>([]);

  useFocusEffect(
    useCallback(() => {
      // Refresh data when screen comes into focus
      setRefreshTrigger(prev => prev + 1);
    }, [])
  );

  const handleResultSelect = (result: SearchResult) => {
    Alert.alert(
      'Content Selected',
      `Selected ${result.content_type} content with ${Math.round(result.similarity_score * 100)}% similarity.`,
      [
        { text: 'OK' },
        {
          text: 'View Related',
          onPress: () => {
            // Navigate to related content or show more details
            console.log('View related content for:', result.content_id);
          },
        },
      ]
    );
  };

  const handleQuestionSubmitted = (questionId: string) => {
    Alert.alert(
      'Question Submitted',
      'Your question has been submitted and is being processed. You can view the answer in the Questions tab.',
      [
        { text: 'OK' },
        {
          text: 'View Questions',
          onPress: () => {
            // Navigate to questions screen
            console.log('Navigate to questions with ID:', questionId);
          },
        },
      ]
    );
  };

  const toggleContentType = (contentType: string) => {
    setSelectedContentTypes(prev => {
      if (prev.includes(contentType)) {
        return prev.filter(type => type !== contentType);
      } else {
        return [...prev, contentType];
      }
    });
  };

  const renderContentTypeFilters = () => {
    const contentTypes = [
      { key: 'text', label: 'Text', icon: 'document-text-outline' },
      { key: 'image', label: 'Images', icon: 'image-outline' },
      { key: 'audio', label: 'Audio', icon: 'musical-notes-outline' },
    ];

    return (
      <View style={styles.filtersContainer}>
        <Text style={styles.filtersTitle}>Filter by type:</Text>
        <View style={styles.filtersList}>
          {contentTypes.map(type => (
            <TouchableOpacity
              key={type.key}
              style={[
                styles.filterChip,
                selectedContentTypes.includes(type.key) && styles.filterChipActive,
              ]}
              onPress={() => toggleContentType(type.key)}
            >
              <Ionicons
                name={type.icon as any}
                size={16}
                color={selectedContentTypes.includes(type.key) ? '#fff' : '#666'}
              />
              <Text
                style={[
                  styles.filterText,
                  selectedContentTypes.includes(type.key) && styles.filterTextActive,
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderTabButton = (tab: TabType, label: string, icon: string) => (
    <TouchableOpacity
      style={[styles.tabButton, activeTab === tab && styles.tabButtonActive]}
      onPress={() => setActiveTab(tab)}
    >
      <Ionicons
        name={icon as any}
        size={20}
        color={activeTab === tab ? '#007bff' : '#666'}
      />
      <Text
        style={[
          styles.tabText,
          activeTab === tab && styles.tabTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'search':
        return (
          <View style={styles.tabContent}>
            {renderContentTypeFilters()}
            <SemanticSearch
              onResultSelect={handleResultSelect}
              contentTypes={selectedContentTypes.length > 0 ? selectedContentTypes : undefined}
              placeholder="Search your content using natural language..."
            />
          </View>
        );
      
      case 'insights':
        return (
          <View style={styles.tabContent}>
            <ContentInsights
              refreshTrigger={refreshTrigger}
            />
          </View>
        );
      
      case 'ask':
        return (
          <View style={styles.tabContent}>
            <ScrollView style={styles.askContainer} showsVerticalScrollIndicator={false}>
              <View style={styles.askHeader}>
                <Ionicons name="sparkles" size={24} color="#007bff" />
                <Text style={styles.askTitle}>Ask AI Assistant</Text>
              </View>
              <Text style={styles.askDescription}>
                Ask questions about your content and get intelligent answers based on your uploaded images, 
                audio recordings, and text. The AI will search through your content to provide contextual responses.
              </Text>
              
              <View style={styles.exampleQuestions}>
                <Text style={styles.exampleTitle}>Example questions:</Text>
                <View style={styles.exampleList}>
                  <Text style={styles.exampleItem}>• "What themes do you see in my photos?"</Text>
                  <Text style={styles.exampleItem}>• "Summarize my recent voice notes"</Text>
                  <Text style={styles.exampleItem}>• "Find content related to travel"</Text>
                  <Text style={styles.exampleItem}>• "What questions have I asked about work?"</Text>
                </View>
              </View>
            </ScrollView>
            
            <EnhancedQuestionInput
              onQuestionSubmitted={handleQuestionSubmitted}
              showAudioInput={true}
              showImageContext={true}
            />
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Intelligent Search</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={() => setRefreshTrigger(prev => prev + 1)}
        >
          <Ionicons name="refresh-outline" size={24} color="#007bff" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {renderTabButton('search', 'Search', 'search-outline')}
        {renderTabButton('insights', 'Insights', 'analytics-outline')}
        {renderTabButton('ask', 'Ask AI', 'chatbubble-ellipses-outline')}
      </View>

      {/* Tab Content */}
      {renderTabContent()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  refreshButton: {
    padding: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  tabButtonActive: {
    borderBottomWidth: 2,
    borderBottomColor: '#007bff',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  tabTextActive: {
    color: '#007bff',
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
  },
  filtersContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  filtersTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    fontWeight: '600',
  },
  filtersList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  filterChipActive: {
    backgroundColor: '#007bff',
    borderColor: '#007bff',
  },
  filterText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    fontWeight: '500',
  },
  filterTextActive: {
    color: '#fff',
  },
  askContainer: {
    flex: 1,
  },
  askHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  askTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginLeft: 12,
  },
  askDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: '#fff',
  },
  exampleQuestions: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  exampleTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  exampleList: {
    marginLeft: 8,
  },
  exampleItem: {
    fontSize: 13,
    color: '#666',
    lineHeight: 20,
    marginBottom: 4,
  },
});
