import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Button } from '@/components/Button';
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '@/constants';

export function AskQuestionScreen() {
  const [questionText, setQuestionText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTextSubmit = async () => {
    if (!questionText.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Question Required',
        text2: 'Please enter your question',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit question
      await new Promise(resolve => setTimeout(resolve, 2000)); // Mock delay
      
      Toast.show({
        type: 'success',
        text1: 'Question Submitted',
        text2: 'Your question has been submitted successfully',
      });
      
      setQuestionText('');
      setSelectedImages([]);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Submission Failed',
        text2: 'Failed to submit your question. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAudioRecord = () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      Toast.show({
        type: 'info',
        text1: 'Recording Stopped',
        text2: 'Audio recording has been stopped',
      });
    } else {
      // Start recording
      setIsRecording(true);
      Toast.show({
        type: 'info',
        text1: 'Recording Started',
        text2: 'Speak your question now',
      });
    }
  };

  const handleImagePicker = () => {
    Alert.alert(
      'Add Images',
      'Choose how you want to add images',
      [
        { text: 'Camera', onPress: () => handleImageSource('camera') },
        { text: 'Gallery', onPress: () => handleImageSource('gallery') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleImageSource = (source: 'camera' | 'gallery') => {
    // TODO: Implement image picker
    Toast.show({
      type: 'info',
      text1: 'Image Picker',
      text2: `${source} functionality will be implemented`,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Text Question Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ask Your Question</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Type your question here..."
              placeholderTextColor={COLORS.textSecondary}
              value={questionText}
              onChangeText={setQuestionText}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
          <Button
            title="Submit Question"
            onPress={handleTextSubmit}
            variant="primary"
            size="large"
            loading={isSubmitting}
            disabled={!questionText.trim()}
          />
        </View>

        {/* Divider */}
        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>OR</Text>
          <View style={styles.dividerLine} />
        </View>

        {/* Audio Question Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Record Audio Question</Text>
          <TouchableOpacity
            style={[
              styles.recordButton,
              isRecording && styles.recordButtonActive,
            ]}
            onPress={handleAudioRecord}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isRecording ? 'stop' : 'mic'}
              size={32}
              color={isRecording ? COLORS.error : COLORS.white}
            />
            <Text style={styles.recordButtonText}>
              {isRecording ? 'Stop Recording' : 'Start Recording'}
            </Text>
          </TouchableOpacity>
          {isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording...</Text>
            </View>
          )}
        </View>

        {/* Context Images Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Add Context Images (Optional)</Text>
          <TouchableOpacity
            style={styles.imagePickerButton}
            onPress={handleImagePicker}
            activeOpacity={0.7}
          >
            <Ionicons name="camera-outline" size={24} color={COLORS.primary} />
            <Text style={styles.imagePickerText}>Add Images</Text>
          </TouchableOpacity>
          
          {selectedImages.length > 0 && (
            <View style={styles.selectedImagesContainer}>
              <Text style={styles.selectedImagesText}>
                {selectedImages.length} image(s) selected
              </Text>
            </View>
          )}
        </View>

        {/* Tips Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tips for Better Answers</Text>
          <View style={styles.tipsContainer}>
            <View style={styles.tipItem}>
              <Ionicons name="checkmark-circle" size={16} color={COLORS.success} />
              <Text style={styles.tipText}>Be specific and clear in your question</Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons name="checkmark-circle" size={16} color={COLORS.success} />
              <Text style={styles.tipText}>Add relevant images for visual context</Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons name="checkmark-circle" size={16} color={COLORS.success} />
              <Text style={styles.tipText}>Use audio for complex explanations</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  
  scrollView: {
    flex: 1,
  },
  
  section: {
    padding: SPACING.lg,
  },
  
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  
  inputContainer: {
    marginBottom: SPACING.md,
  },
  
  textInput: {
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.text,
    minHeight: 120,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    marginVertical: SPACING.md,
  },
  
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.border,
  },
  
  dividerText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    marginHorizontal: SPACING.md,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  
  recordButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.md,
  },
  
  recordButtonActive: {
    backgroundColor: COLORS.error,
  },
  
  recordButtonText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.white,
    marginTop: SPACING.sm,
  },
  
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
  },
  
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.error,
    marginRight: SPACING.sm,
  },
  
  recordingText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.error,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.white,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderStyle: 'dashed',
  },
  
  imagePickerText: {
    fontSize: TYPOGRAPHY.sizes.md,
    color: COLORS.primary,
    marginLeft: SPACING.sm,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  
  selectedImagesContainer: {
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.sm,
  },
  
  selectedImagesText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  
  tipsContainer: {
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
  },
  
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  
  tipText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    flex: 1,
  },
});
