import 'react-native-url-polyfill/auto';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';

import { SUPABASE_CONFIG, STORAGE_KEYS } from '@/constants';
import type { User, AuthResponse } from '@/types';

class SupabaseService {
  private client: SupabaseClient | null = null;

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    if (SUPABASE_CONFIG.USE_SUPABASE && SUPABASE_CONFIG.URL && SUPABASE_CONFIG.ANON_KEY) {
      this.client = createClient(
        SUPABASE_CONFIG.URL,
        SUPABASE_CONFIG.ANON_KEY,
        {
          auth: {
            storage: {
              getItem: async (key: string) => {
                return await SecureStore.getItemAsync(key);
              },
              setItem: async (key: string, value: string) => {
                await SecureStore.setItemAsync(key, value);
              },
              removeItem: async (key: string) => {
                await SecureStore.deleteItemAsync(key);
              },
            },
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: false,
          },
        }
      );
    }
  }

  isAvailable(): boolean {
    return this.client !== null;
  }

  getClient(): SupabaseClient | null {
    return this.client;
  }

  // Authentication methods
  async signInWithOAuth(provider: 'apple' | 'google' | 'azure') {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { data, error } = await this.client.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: 'wheresz://auth/callback',
      },
    });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async signInWithIdToken(provider: 'apple' | 'google', idToken: string, nonce?: string) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { data, error } = await this.client.auth.signInWithIdToken({
      provider,
      token: idToken,
      nonce,
    });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async signOut() {
    if (!this.client) {
      return;
    }

    const { error } = await this.client.auth.signOut();
    if (error) {
      console.warn('Supabase sign out error:', error);
    }
  }

  async getSession() {
    if (!this.client) {
      return null;
    }

    const { data: { session }, error } = await this.client.auth.getSession();
    if (error) {
      console.warn('Error getting session:', error);
      return null;
    }

    return session;
  }

  async getUser() {
    if (!this.client) {
      return null;
    }

    const { data: { user }, error } = await this.client.auth.getUser();
    if (error) {
      console.warn('Error getting user:', error);
      return null;
    }

    return user;
  }

  // Database methods
  async uploadImage(file: Blob, fileName: string, userId: string) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const filePath = `${userId}/${fileName}`;
    
    const { data, error } = await this.client.storage
      .from('images')
      .upload(filePath, file);

    if (error) {
      throw new Error(error.message);
    }

    // Get public URL
    const { data: { publicUrl } } = this.client.storage
      .from('images')
      .getPublicUrl(filePath);

    return {
      path: data.path,
      url: publicUrl,
    };
  }

  async deleteImage(filePath: string) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { error } = await this.client.storage
      .from('images')
      .remove([filePath]);

    if (error) {
      throw new Error(error.message);
    }
  }

  async getUserImages(userId: string, page: number = 1, limit: number = 20) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const offset = (page - 1) * limit;

    const { data, error, count } = await this.client
      .from('images')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(error.message);
    }

    return {
      images: data || [],
      total: count || 0,
      page,
      limit,
      pages: Math.ceil((count || 0) / limit),
      has_next: page < Math.ceil((count || 0) / limit),
      has_prev: page > 1,
    };
  }

  async createQuestion(questionData: {
    content: string;
    type: 'text' | 'audio';
    audio_url?: string;
    context_images?: string[];
    user_id: string;
  }) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { data, error } = await this.client
      .from('questions')
      .insert([questionData])
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async getUserQuestions(userId: string, page: number = 1, limit: number = 20) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const offset = (page - 1) * limit;

    const { data, error, count } = await this.client
      .from('questions')
      .select(`
        *,
        context_images:question_image_context(
          image:images(*)
        ),
        answer:answers(*)
      `, { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(error.message);
    }

    return {
      questions: data || [],
      total: count || 0,
      page,
      limit,
      pages: Math.ceil((count || 0) / limit),
      has_next: page < Math.ceil((count || 0) / limit),
      has_prev: page > 1,
    };
  }

  async getQuestion(questionId: string) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { data, error } = await this.client
      .from('questions')
      .select(`
        *,
        context_images:question_image_context(
          image:images(*)
        ),
        answer:answers(
          *,
          images:answer_images(
            image:images(*)
          )
        )
      `)
      .eq('id', questionId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  async updateUserProfile(userId: string, updates: {
    name?: string;
    first_name?: string;
    last_name?: string;
  }) {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    const { data, error } = await this.client
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }

  // Real-time subscriptions
  subscribeToUserQuestions(userId: string, callback: (payload: any) => void) {
    if (!this.client) {
      return null;
    }

    return this.client
      .channel('user-questions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'questions',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  subscribeToQuestionAnswers(questionId: string, callback: (payload: any) => void) {
    if (!this.client) {
      return null;
    }

    return this.client
      .channel('question-answers')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'answers',
          filter: `question_id=eq.${questionId}`,
        },
        callback
      )
      .subscribe();
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();
