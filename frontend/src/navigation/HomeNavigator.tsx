import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { HomeScreen } from '@/screens/home/<USER>';
import { AskQuestionScreen } from '@/screens/home/<USER>';
import { QuestionDetailScreen } from '@/screens/questions/QuestionDetailScreen';
import type { HomeStackParamList } from '@/types';

const Stack = createNativeStackNavigator<HomeStackParamList>();

export function HomeNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeScreen" 
        component={HomeScreen}
        options={{ title: 'WhereZ' }}
      />
      <Stack.Screen 
        name="AskQuestion" 
        component={AskQuestionScreen}
        options={{ title: 'Ask Question' }}
      />
      <Stack.Screen 
        name="QuestionDetail" 
        component={QuestionDetailScreen}
        options={{ title: 'Question Details' }}
      />
    </Stack.Navigator>
  );
}
