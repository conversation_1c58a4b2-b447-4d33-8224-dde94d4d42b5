import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { ImagesListScreen } from '@/screens/images/ImagesListScreen';
import { ImageDetailScreen } from '@/screens/images/ImageDetailScreen';
import { ImageUploadScreen } from '@/screens/images/ImageUploadScreen';
import type { ImagesStackParamList } from '@/types';

const Stack = createNativeStackNavigator<ImagesStackParamList>();

export function ImagesNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ImagesList" 
        component={ImagesListScreen}
        options={{ title: 'My Images' }}
      />
      <Stack.Screen 
        name="ImageDetail" 
        component={ImageDetailScreen}
        options={{ title: 'Image Details' }}
      />
      <Stack.Screen 
        name="ImageUpload" 
        component={ImageUploadScreen}
        options={{ title: 'Upload Image' }}
      />
    </Stack.Navigator>
  );
}
