{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/hooks/*": ["src/hooks/*"], "@/context/*": ["src/context/*"], "@/constants/*": ["src/constants/*"]}, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": false, "esModuleInterop": true, "isolatedModules": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}