{"expo": {"name": "WhereZ", "slug": "wheresz-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.wheresz.mobile", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take photos for questions.", "NSMicrophoneUsageDescription": "This app needs access to microphone to record audio questions.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images for questions."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.wheresz.mobile", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-apple-authentication", ["expo-camera", {"cameraPermission": "Allow Where<PERSON> to access your camera to take photos for questions."}], ["expo-image-picker", {"photosPermission": "Allow WhereZ to access your photos to select images for questions.", "cameraPermission": "Allow Where<PERSON> to access your camera to take photos for questions."}], ["expo-av", {"microphonePermission": "Allow Where<PERSON> to access your microphone to record audio questions."}]], "scheme": "wheresz", "extra": {"eas": {"projectId": "your-eas-project-id"}}}}