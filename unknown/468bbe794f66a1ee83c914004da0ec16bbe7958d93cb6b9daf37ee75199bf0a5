import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { QuestionsListScreen } from '@/screens/questions/QuestionsListScreen';
import { QuestionDetailScreen } from '@/screens/questions/QuestionDetailScreen';
import type { QuestionsStackParamList } from '@/types';

const Stack = createNativeStackNavigator<QuestionsStackParamList>();

export function QuestionsNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="QuestionsList" 
        component={QuestionsListScreen}
        options={{ title: 'My Questions' }}
      />
      <Stack.Screen 
        name="QuestionDetail" 
        component={QuestionDetailScreen}
        options={{ title: 'Question Details' }}
      />
    </Stack.Navigator>
  );
}
