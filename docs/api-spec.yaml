openapi: 3.0.3
info:
  title: WhereZ MVP API
  description: |
    Complete API specification for WhereZ mobile application MVP.
    
    Features:
    - OAuth2 authentication (Apple, Google, Microsoft)
    - JWT token management
    - Image upload and management
    - Question & Answer system with text and audio input
    - User profile management
    
  version: 1.0.0
  contact:
    name: WhereZ API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000
    description: Development server
  - url: https://api.wheresz.com
    description: Production server

security:
  - BearerAuth: []

paths:
  # Authentication endpoints
  /auth/oauth/apple:
    post:
      tags:
        - Authentication
      summary: Apple Sign-In
      description: Authenticate user with Apple ID
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppleAuthRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/oauth/google:
    post:
      tags:
        - Authentication
      summary: Google Sign-In
      description: Authenticate user with Google account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoogleAuthRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/oauth/microsoft:
    post:
      tags:
        - Authentication
      summary: Microsoft Sign-In
      description: Authenticate user with Microsoft account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MicrosoftAuthRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh JWT token
      description: Get new access token using refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user
      description: Invalidate user session and tokens
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # User profile endpoints
  /users/me:
    get:
      tags:
        - Users
      summary: Get current user profile
      description: Retrieve authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - Users
      summary: Update user profile
      description: Update authenticated user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserProfile'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Image upload endpoints
  /images/upload:
    post:
      tags:
        - Images
      summary: Upload image
      description: Upload an image file with validation and compression
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ImageUploadRequest'
      responses:
        '201':
          description: Image uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '413':
          description: File too large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /images/{image_id}:
    get:
      tags:
        - Images
      summary: Get image
      description: Retrieve image by ID
      parameters:
        - name: image_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

    delete:
      tags:
        - Images
      summary: Delete image
      description: Delete image by ID
      parameters:
        - name: image_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /images/user:
    get:
      tags:
        - Images
      summary: Get user images
      description: Retrieve all images uploaded by the authenticated user
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Images retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Question & Answer endpoints
  /questions:
    post:
      tags:
        - Questions
      summary: Submit question
      description: Submit a text or audio question
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioQuestionRequest'
      responses:
        '201':
          description: Question submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

    get:
      tags:
        - Questions
      summary: Get user questions
      description: Retrieve question history for authenticated user
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Questions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /questions/{question_id}:
    get:
      tags:
        - Questions
      summary: Get question details
      description: Retrieve specific question and its answer
      parameters:
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Question retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionDetailResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /answers/{question_id}:
    get:
      tags:
        - Answers
      summary: Get answer for question
      description: Retrieve answer for a specific question
      parameters:
        - name: question_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Answer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Health check
  /health:
    get:
      tags:
        - System
      summary: Health check
      description: Check API health status
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Authentication schemas
    AppleAuthRequest:
      type: object
      required:
        - identity_token
        - authorization_code
      properties:
        identity_token:
          type: string
          description: Apple ID token
        authorization_code:
          type: string
          description: Apple authorization code
        user_info:
          type: object
          properties:
            name:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
            email:
              type: string
              format: email

    GoogleAuthRequest:
      type: object
      required:
        - id_token
      properties:
        id_token:
          type: string
          description: Google ID token
        access_token:
          type: string
          description: Google access token

    MicrosoftAuthRequest:
      type: object
      required:
        - access_token
      properties:
        access_token:
          type: string
          description: Microsoft access token
        id_token:
          type: string
          description: Microsoft ID token

    RefreshTokenRequest:
      type: object
      required:
        - refresh_token
      properties:
        refresh_token:
          type: string
          description: Refresh token

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: Refresh token
        token_type:
          type: string
          default: bearer
        expires_in:
          type: integer
          description: Token expiration time in seconds
        user:
          $ref: '#/components/schemas/UserProfile'

    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
        token_type:
          type: string
          default: bearer
        expires_in:
          type: integer
          description: Token expiration time in seconds

    # User schemas
    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User unique identifier
        email:
          type: string
          format: email
          description: User email address
        name:
          type: string
          description: User full name
        first_name:
          type: string
          description: User first name
        last_name:
          type: string
          description: User last name
        avatar_url:
          type: string
          format: uri
          description: User avatar image URL
        provider:
          type: string
          enum: [apple, google, microsoft]
          description: OAuth provider used for authentication
        created_at:
          type: string
          format: date-time
          description: Account creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last profile update timestamp

    UpdateUserProfile:
      type: object
      properties:
        name:
          type: string
          description: User full name
        first_name:
          type: string
          description: User first name
        last_name:
          type: string
          description: User last name

    # Image schemas
    ImageUploadRequest:
      type: object
      required:
        - file
      properties:
        file:
          type: string
          format: binary
          description: Image file to upload
        description:
          type: string
          description: Optional image description

    ImageResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Image unique identifier
        filename:
          type: string
          description: Original filename
        url:
          type: string
          format: uri
          description: Image access URL
        thumbnail_url:
          type: string
          format: uri
          description: Thumbnail image URL
        size:
          type: integer
          description: File size in bytes
        mime_type:
          type: string
          description: Image MIME type
        description:
          type: string
          description: Image description
        created_at:
          type: string
          format: date-time
          description: Upload timestamp
        user_id:
          type: string
          format: uuid
          description: Owner user ID

    ImageListResponse:
      type: object
      properties:
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # Question & Answer schemas
    QuestionRequest:
      type: object
      required:
        - content
        - type
      properties:
        content:
          type: string
          description: Question text content
        type:
          type: string
          enum: [text, audio]
          description: Question input type
        context_images:
          type: array
          items:
            type: string
            format: uuid
          description: Related image IDs for context

    AudioQuestionRequest:
      type: object
      required:
        - audio_file
      properties:
        audio_file:
          type: string
          format: binary
          description: Audio file containing the question
        context_images:
          type: array
          items:
            type: string
            format: uuid
          description: Related image IDs for context

    QuestionResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Question unique identifier
        content:
          type: string
          description: Question text content
        type:
          type: string
          enum: [text, audio]
          description: Question input type
        audio_url:
          type: string
          format: uri
          description: Audio file URL (for audio questions)
        context_images:
          type: array
          items:
            $ref: '#/components/schemas/ImageResponse'
          description: Related images for context
        status:
          type: string
          enum: [pending, processing, completed, failed]
          description: Question processing status
        created_at:
          type: string
          format: date-time
          description: Question submission timestamp
        user_id:
          type: string
          format: uuid
          description: User who submitted the question

    QuestionDetailResponse:
      allOf:
        - $ref: '#/components/schemas/QuestionResponse'
        - type: object
          properties:
            answer:
              $ref: '#/components/schemas/AnswerResponse'

    QuestionListResponse:
      type: object
      properties:
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuestionResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    AnswerResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Answer unique identifier
        content:
          type: string
          description: Answer text content
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageResponse'
          description: Answer images
        confidence:
          type: number
          format: float
          minimum: 0
          maximum: 1
          description: Answer confidence score
        sources:
          type: array
          items:
            type: string
          description: Information sources used
        created_at:
          type: string
          format: date-time
          description: Answer generation timestamp
        question_id:
          type: string
          format: uuid
          description: Related question ID

    # Common schemas
    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
        limit:
          type: integer
          description: Items per page
        total:
          type: integer
          description: Total number of items
        pages:
          type: integer
          description: Total number of pages
        has_next:
          type: boolean
          description: Whether there are more pages
        has_prev:
          type: boolean
          description: Whether there are previous pages

    MessageResponse:
      type: object
      properties:
        message:
          type: string
          description: Response message

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: API health status
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
        version:
          type: string
          description: API version

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
