<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="1" class="java.lang.String" itemvalue="stable-baselines3" />
            <item index="2" class="java.lang.String" itemvalue="gym" />
            <item index="3" class="java.lang.String" itemvalue="matplotlib" />
            <item index="4" class="java.lang.String" itemvalue="nose" />
            <item index="5" class="java.lang.String" itemvalue="openai" />
            <item index="6" class="java.lang.String" itemvalue="google-generativeai" />
            <item index="7" class="java.lang.String" itemvalue="langchain" />
            <item index="8" class="java.lang.String" itemvalue="vertexai" />
            <item index="9" class="java.lang.String" itemvalue="streamlit" />
            <item index="10" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="11" class="java.lang.String" itemvalue="google-cloud-aiplatform" />
            <item index="12" class="java.lang.String" itemvalue="streamlit_ace" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>