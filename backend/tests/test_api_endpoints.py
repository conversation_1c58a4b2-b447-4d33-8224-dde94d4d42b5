"""Tests for API endpoints with LLM functionality."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock, AsyncMock
import json

from app.main import app
from app.models import User, Question, Image, ContentEmbedding, ProcessedContent
from tests.conftest import create_test_user, create_test_question, create_test_image


client = TestClient(app)


class TestSearchEndpoints:
    """Test search API endpoints."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_semantic_search_endpoint(self, db: Session, authenticated_user: User):
        """Test semantic search endpoint."""
        with patch("app.services.intelligent_query_service.intelligent_query_service.semantic_search") as mock_search:
            mock_embedding = MagicMock()
            mock_embedding.content_id = "test-id"
            mock_embedding.content_type.value = "text"
            mock_embedding.content_text = "Test content"
            mock_embedding.metadata = {}
            mock_embedding.created_at.isoformat.return_value = "2024-01-01T00:00:00"
            
            mock_search.return_value = [(mock_embedding, 0.9)]
            
            response = client.post("/search/semantic", json={
                "query": "test query",
                "limit": 10,
                "similarity_threshold": 0.7
            })
            
            assert response.status_code == 200
            data = response.json()
            assert "query" in data
            assert "results" in data
            assert "total_results" in data
            assert len(data["results"]) == 1
            assert data["results"][0]["similarity_score"] == 0.9
    
    def test_semantic_search_validation(self, db: Session, authenticated_user: User):
        """Test semantic search input validation."""
        # Test missing query
        response = client.post("/search/semantic", json={
            "limit": 10
        })
        assert response.status_code == 422
        
        # Test invalid limit
        response = client.post("/search/semantic", json={
            "query": "test",
            "limit": 0
        })
        assert response.status_code == 422
        
        # Test invalid similarity threshold
        response = client.post("/search/semantic", json={
            "query": "test",
            "similarity_threshold": 1.5
        })
        assert response.status_code == 422
    
    def test_content_insights_endpoint(self, db: Session, authenticated_user: User):
        """Test content insights endpoint."""
        with patch("app.services.intelligent_query_service.intelligent_query_service.get_content_insights") as mock_insights:
            mock_insights.return_value = {
                "total_items": 5,
                "insights": "Test insights",
                "common_themes": ["theme1", "theme2"],
                "sentiment_distribution": {"positive": 3, "neutral": 2},
                "tag_frequency": {"tag1": 5, "tag2": 3}
            }
            
            response = client.get("/search/insights")
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_items"] == 5
            assert "insights" in data
            assert "common_themes" in data
    
    def test_related_content_endpoint(self, db: Session, authenticated_user: User):
        """Test related content endpoint."""
        with patch("app.services.intelligent_query_service.intelligent_query_service.suggest_related_content") as mock_related:
            mock_embedding = MagicMock()
            mock_embedding.content_id = "related-id"
            mock_embedding.content_type.value = "text"
            mock_embedding.content_text = "Related content"
            mock_embedding.metadata = {}
            mock_embedding.created_at.isoformat.return_value = "2024-01-01T00:00:00"
            
            mock_related.return_value = [(mock_embedding, 0.8)]
            
            response = client.get("/search/related/test-id?content_type=text")
            
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["similarity_score"] == 0.8


class TestContentEndpoints:
    """Test content processing API endpoints."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_process_content_endpoint(self, db: Session, authenticated_user: User):
        """Test content processing endpoint."""
        # Create test image
        image = create_test_image(db, user_id=authenticated_user.id)
        
        response = client.post("/content/process", json={
            "content_ids": [str(image.id)],
            "content_type": "image",
            "force_reprocess": False
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "processed_count" in data
        assert "failed_count" in data
        assert "processing_ids" in data
    
    def test_get_processed_content_endpoint(self, db: Session, authenticated_user: User):
        """Test get processed content endpoint."""
        # Create test processed content
        processed = ProcessedContent(
            content_id=authenticated_user.id,
            content_type="text",
            processed_content="Processed text",
            summary="Test summary",
            key_points=["point1", "point2"],
            tags=["tag1", "tag2"],
            sentiment="positive",
            confidence=0.9,
            llm_model="gpt-4",
            user_id=authenticated_user.id
        )
        db.add(processed)
        db.commit()
        
        response = client.get("/content/processed")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["summary"] == "Test summary"
        assert data[0]["sentiment"] == "positive"
    
    def test_get_embeddings_endpoint(self, db: Session, authenticated_user: User):
        """Test get embeddings endpoint."""
        # Create test embedding
        embedding = ContentEmbedding(
            content_id=authenticated_user.id,
            content_type="text",
            content_text="Test content",
            metadata={"key": "value"},
            user_id=authenticated_user.id
        )
        db.add(embedding)
        db.commit()
        
        response = client.get("/content/embeddings")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["content_text"] == "Test content"
        assert data[0]["metadata"] == {"key": "value"}
    
    def test_reindex_content_endpoint(self, db: Session, authenticated_user: User):
        """Test content reindexing endpoint."""
        with patch("app.services.embeddings_service.embeddings_service.reindex_user_content") as mock_reindex:
            mock_reindex.return_value = {"reindexed_count": 5, "user_id": str(authenticated_user.id)}
            
            response = client.post("/content/reindex")
            
            assert response.status_code == 200
            data = response.json()
            assert "message" in data
            assert "user_id" in data


class TestEnhancedQuestionEndpoints:
    """Test enhanced question endpoints with LLM functionality."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_question_with_llm_processing(self, db: Session, authenticated_user: User):
        """Test question creation with LLM processing."""
        with patch("app.services.question_service.QuestionService.generate_answer") as mock_generate:
            mock_answer = MagicMock()
            mock_answer.content = "LLM generated answer"
            mock_answer.confidence = 0.9
            mock_generate.return_value = mock_answer
            
            response = client.post("/questions", json={
                "content": "What is machine learning?",
                "type": "text"
            })
            
            assert response.status_code == 201
            data = response.json()
            assert data["content"] == "What is machine learning?"
            assert data["type"] == "text"
    
    def test_audio_question_with_transcription(self, db: Session, authenticated_user: User):
        """Test audio question with transcription."""
        with patch("app.services.question_service.QuestionService.transcribe_audio") as mock_transcribe:
            mock_transcribe.return_value = "Transcribed audio content"
            
            with patch("app.services.question_service.QuestionService.generate_answer") as mock_generate:
                mock_answer = MagicMock()
                mock_generate.return_value = mock_answer
                
                # Mock file upload
                audio_data = b"fake_audio_data"
                response = client.post(
                    "/questions/audio",
                    files={"file": ("test.mp3", audio_data, "audio/mpeg")},
                    data={"content": "Audio question"}
                )
                
                assert response.status_code == 201


class TestImageEndpointsWithLLM:
    """Test image endpoints with LLM analysis."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_image_upload_with_analysis(self, db: Session, authenticated_user: User):
        """Test image upload with LLM analysis."""
        with patch("app.services.image_service.ImageService.process_image") as mock_process:
            mock_process.return_value = (b"compressed", b"thumbnail", (800, 600))
            
            with patch("app.services.image_service.ImageService.save_image_files") as mock_save:
                mock_save.return_value = ("path", "thumb_path", "url", "thumb_url")
                
                with patch("app.services.content_processor.content_processor.process_image") as mock_llm_process:
                    mock_llm_process.return_value = (MagicMock(), MagicMock())
                    
                    # Mock file upload
                    image_data = b"fake_image_data"
                    response = client.post(
                        "/images/upload",
                        files={"file": ("test.jpg", image_data, "image/jpeg")},
                        data={"description": "Test image"}
                    )
                    
                    assert response.status_code == 201


class TestAnswerEndpointsWithLLM:
    """Test answer endpoints with LLM functionality."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_get_answer_with_llm_reasoning(self, db: Session, authenticated_user: User):
        """Test getting answer with LLM reasoning."""
        # Create question and answer
        question = create_test_question(db, user_id=authenticated_user.id)
        
        from app.models import Answer
        answer = Answer(
            content="LLM generated answer",
            confidence=0.9,
            sources=["AI Analysis", "User Content"],
            llm_model="gpt-4",
            reasoning="Answer based on semantic analysis of user content",
            question_id=question.id
        )
        db.add(answer)
        db.commit()
        
        response = client.get(f"/answers/{question.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "LLM generated answer"
        assert data["confidence"] == 0.9
        assert "llm_model" in data
        assert "reasoning" in data


class TestErrorHandling:
    """Test error handling in API endpoints."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_llm_service_error_handling(self, db: Session, authenticated_user: User):
        """Test handling of LLM service errors."""
        with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
            mock_embeddings.side_effect = Exception("LLM service unavailable")
            
            response = client.post("/search/semantic", json={
                "query": "test query",
                "limit": 10
            })
            
            assert response.status_code == 500
            assert "error" in response.json()["detail"].lower()
    
    def test_database_error_handling(self, db: Session, authenticated_user: User):
        """Test handling of database errors."""
        with patch("sqlalchemy.orm.Session.commit") as mock_commit:
            mock_commit.side_effect = Exception("Database connection error")
            
            response = client.post("/content/process", json={
                "content_ids": ["test-id"],
                "content_type": "text"
            })
            
            # Should handle database errors gracefully
            assert response.status_code in [500, 503]
    
    def test_invalid_content_id_handling(self, db: Session, authenticated_user: User):
        """Test handling of invalid content IDs."""
        response = client.get("/search/related/invalid-uuid?content_type=text")
        
        # Should handle invalid UUIDs gracefully
        assert response.status_code in [400, 422, 500]
    
    def test_large_query_handling(self, db: Session, authenticated_user: User):
        """Test handling of very large queries."""
        large_query = "A" * 10000  # 10KB query
        
        response = client.post("/search/semantic", json={
            "query": large_query,
            "limit": 10
        })
        
        # Should handle large queries gracefully
        assert response.status_code in [200, 400, 413, 422]


class TestPerformanceAndLimits:
    """Test performance and rate limiting."""
    
    @pytest.fixture
    def authenticated_user(self, db: Session):
        """Create an authenticated user for testing."""
        user = create_test_user(db)
        with patch("app.auth.dependencies.get_current_user", return_value=user):
            yield user
    
    def test_search_result_limits(self, db: Session, authenticated_user: User):
        """Test that search results respect limits."""
        with patch("app.services.intelligent_query_service.intelligent_query_service.semantic_search") as mock_search:
            # Mock many results
            mock_results = [(MagicMock(), 0.9) for _ in range(100)]
            mock_search.return_value = mock_results
            
            response = client.post("/search/semantic", json={
                "query": "test",
                "limit": 5
            })
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["results"]) <= 5
    
    def test_batch_processing_limits(self, db: Session, authenticated_user: User):
        """Test that batch processing respects limits."""
        # Try to process many items at once
        many_ids = [str(i) for i in range(1000)]
        
        response = client.post("/content/process", json={
            "content_ids": many_ids,
            "content_type": "text"
        })
        
        # Should handle large batches gracefully
        assert response.status_code in [200, 400, 413, 422]
