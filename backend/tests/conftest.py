"""Test configuration and fixtures."""

import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from unittest.mock import patch

from app.database import Base, get_db
from app.main import app
from app.models import User, Question, Image, Answer, AuthToken, ContentEmbedding, ProcessedContent
from app.models.user import OAuthProvider
from app.models.question import QuestionType, QuestionStatus
from app.models.image import ProcessingStatus
from app.models.content_embedding import ContentType


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db(db_engine):
    """Create test database session."""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client(db):
    """Create test client with database dependency override."""
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()


@pytest.fixture
def temp_upload_dir():
    """Create temporary upload directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        with patch("app.config.settings.UPLOAD_DIR", temp_dir):
            yield temp_dir


# Test data creation helpers
def create_test_user(
    db,
    email: str = "<EMAIL>",
    name: str = "Test User",
    provider: OAuthProvider = OAuthProvider.GOOGLE,
    provider_id: str = "test_provider_id"
) -> User:
    """Create a test user."""
    user = User(
        email=email,
        name=name,
        first_name="Test",
        last_name="User",
        provider=provider,
        provider_id=provider_id
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def create_test_question(
    db,
    user_id: str = None,
    content: str = "Test question?",
    question_type: QuestionType = QuestionType.TEXT,
    status: QuestionStatus = QuestionStatus.PENDING
) -> Question:
    """Create a test question."""
    if user_id is None:
        user = create_test_user(db)
        user_id = user.id
    
    question = Question(
        content=content,
        type=question_type,
        status=status,
        user_id=user_id
    )
    db.add(question)
    db.commit()
    db.refresh(question)
    return question


def create_test_image(
    db,
    user_id: str = None,
    filename: str = "test.jpg",
    original_filename: str = "test_original.jpg",
    description: str = "Test image"
) -> Image:
    """Create a test image."""
    if user_id is None:
        user = create_test_user(db)
        user_id = user.id
    
    image = Image(
        filename=filename,
        original_filename=original_filename,
        file_path=f"/uploads/images/{user_id}/{filename}",
        url=f"/uploads/images/{user_id}/{filename}",
        size=1024,
        mime_type="image/jpeg",
        width=800,
        height=600,
        description=description,
        processing_status=ProcessingStatus.PENDING,
        user_id=user_id
    )
    db.add(image)
    db.commit()
    db.refresh(image)
    return image


def create_test_answer(
    db,
    question_id: str,
    content: str = "Test answer",
    confidence: float = 0.85
) -> Answer:
    """Create a test answer."""
    answer = Answer(
        content=content,
        confidence=confidence,
        sources=["Test Source"],
        llm_model="test-model",
        reasoning="Test reasoning",
        question_id=question_id
    )
    db.add(answer)
    db.commit()
    db.refresh(answer)
    return answer


def create_test_auth_token(
    db,
    user_id: str,
    token: str = "test_token",
    jti: str = "test_jti"
) -> AuthToken:
    """Create a test auth token."""
    from datetime import datetime, timedelta
    
    auth_token = AuthToken(
        token=token,
        jti=jti,
        expires_at=datetime.utcnow() + timedelta(hours=1),
        user_id=user_id
    )
    db.add(auth_token)
    db.commit()
    db.refresh(auth_token)
    return auth_token


def create_test_content_embedding(
    db,
    user_id: str = None,
    content_id: str = None,
    content_type: ContentType = ContentType.TEXT,
    content_text: str = "Test content for embedding"
) -> ContentEmbedding:
    """Create a test content embedding."""
    if user_id is None:
        user = create_test_user(db)
        user_id = user.id
    
    if content_id is None:
        content_id = user_id
    
    embedding = ContentEmbedding(
        content_id=content_id,
        content_type=content_type,
        content_text=content_text,
        embedding=[0.1] * 1536,  # Mock embedding vector
        metadata={"test": "metadata"},
        user_id=user_id
    )
    db.add(embedding)
    db.commit()
    db.refresh(embedding)
    return embedding


def create_test_processed_content(
    db,
    user_id: str = None,
    content_id: str = None,
    content_type: ContentType = ContentType.TEXT,
    processed_content: str = "Processed test content"
) -> ProcessedContent:
    """Create test processed content."""
    if user_id is None:
        user = create_test_user(db)
        user_id = user.id
    
    if content_id is None:
        content_id = user_id
    
    processed = ProcessedContent(
        content_id=content_id,
        content_type=content_type,
        original_content="Original test content",
        processed_content=processed_content,
        summary="Test summary",
        key_points=["point1", "point2"],
        tags=["tag1", "tag2"],
        sentiment="positive",
        confidence=0.9,
        llm_model="test-model",
        processing_metadata={"test": "metadata"},
        user_id=user_id
    )
    db.add(processed)
    db.commit()
    db.refresh(processed)
    return processed


# Mock fixtures for external services
@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    with patch("app.services.llm_service.openai.AsyncOpenAI") as mock_client:
        # Mock common responses
        mock_response = type('MockResponse', (), {})()
        mock_response.choices = [type('Choice', (), {})()]
        mock_response.choices[0].message = type('Message', (), {})()
        mock_response.choices[0].message.content = "Mock response"
        
        mock_embedding_response = type('MockEmbeddingResponse', (), {})()
        mock_embedding_response.data = [type('EmbeddingData', (), {})()]
        mock_embedding_response.data[0].embedding = [0.1] * 1536
        
        mock_client.return_value.chat.completions.create.return_value = mock_response
        mock_client.return_value.embeddings.create.return_value = mock_embedding_response
        mock_client.return_value.audio.transcriptions.create.return_value = "Mock transcription"
        
        yield mock_client


@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing."""
    with patch("app.services.supabase_service.create_client") as mock_client:
        yield mock_client


@pytest.fixture
def mock_file_storage():
    """Mock file storage operations."""
    with patch("app.services.image_service.ImageService.save_image_files") as mock_save:
        mock_save.return_value = (
            "/test/path/image.jpg",
            "/test/path/thumb.jpg", 
            "/uploads/image.jpg",
            "/uploads/thumb.jpg"
        )
        yield mock_save


# Test environment setup
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    test_env = {
        "DATABASE_URL": "sqlite:///./test.db",
        "JWT_SECRET_KEY": "test-secret-key",
        "DEBUG": "true",
        "OPENAI_API_KEY": "test-openai-key",
        "UPLOAD_DIR": "/tmp/test_uploads"
    }
    
    with patch.dict(os.environ, test_env):
        yield


# Async test helpers
@pytest.fixture
def event_loop():
    """Create event loop for async tests."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Authentication helpers
def create_auth_headers(token: str = "test_token") -> dict:
    """Create authentication headers for testing."""
    return {"Authorization": f"Bearer {token}"}


def mock_authenticated_user(user: User):
    """Mock authenticated user for testing."""
    return patch("app.auth.dependencies.get_current_user", return_value=user)


# Database state helpers
def clear_database(db):
    """Clear all data from test database."""
    for table in reversed(Base.metadata.sorted_tables):
        db.execute(table.delete())
    db.commit()


def count_table_rows(db, model_class):
    """Count rows in a table."""
    return db.query(model_class).count()


# File testing helpers
def create_test_image_file(filename: str = "test.jpg") -> bytes:
    """Create test image file data."""
    # Simple JPEG header
    return b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb'


def create_test_audio_file(filename: str = "test.mp3") -> bytes:
    """Create test audio file data."""
    # Simple MP3 header
    return b'\xff\xfb\x90\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'


# Performance testing helpers
@pytest.fixture
def performance_timer():
    """Timer for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()
