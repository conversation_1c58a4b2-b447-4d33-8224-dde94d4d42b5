"""Authentication tests."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.database import Base, get_db
from app.models import User, OAuthProvider

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_apple_signin_invalid_token():
    """Test Apple Sign-In with invalid token."""
    response = client.post("/auth/oauth/apple", json={
        "identity_token": "invalid_token",
        "authorization_code": "invalid_code"
    })
    assert response.status_code == 401


def test_google_signin_invalid_token():
    """Test Google Sign-In with invalid token."""
    response = client.post("/auth/oauth/google", json={
        "id_token": "invalid_token"
    })
    assert response.status_code == 401


def test_microsoft_signin_invalid_token():
    """Test Microsoft Sign-In with invalid token."""
    response = client.post("/auth/oauth/microsoft", json={
        "access_token": "invalid_token"
    })
    assert response.status_code == 401


def test_refresh_token_invalid():
    """Test refresh token with invalid token."""
    response = client.post("/auth/refresh", json={
        "refresh_token": "invalid_token"
    })
    assert response.status_code == 401


def test_logout_unauthorized():
    """Test logout without authentication."""
    response = client.post("/auth/logout")
    assert response.status_code == 401


# Add more comprehensive tests here for successful authentication flows
# These would require mocking the OAuth providers
