"""Database configuration and session management."""

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.config import settings
from app.services.supabase_service import supabase_service

# Determine database URLs (Supabase takes precedence)
database_url = settings.DATABASE_URL
async_database_url = settings.DATABASE_URL_ASYNC

if settings.USE_SUPABASE and supabase_service.is_available():
    supabase_db_url = supabase_service.get_database_url()
    supabase_async_db_url = supabase_service.get_async_database_url()

    if supabase_db_url and supabase_async_db_url:
        database_url = supabase_db_url
        async_database_url = supabase_async_db_url

# Create database engines
if database_url.startswith("sqlite"):
    # SQLite configuration for development
    engine = create_engine(
        database_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    async_engine = create_async_engine(
        async_database_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
else:
    # PostgreSQL configuration for production (including Supabase)
    engine = create_engine(database_url)
    async_engine = create_async_engine(async_database_url)

# Create session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

# Create declarative base
Base = declarative_base()


def get_db():
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """Dependency to get async database session."""
    async with AsyncSessionLocal() as session:
        yield session


async def init_db():
    """Initialize database tables."""
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """Close database connections."""
    await async_engine.dispose()
