"""Question schemas."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .answer import AnswerResponse
from .image import ImageResponse
from .common import PaginationInfo


class QuestionRequest(BaseModel):
    """Question request schema."""
    content: str = Field(..., description="Question text content")
    type: str = Field(default="text", description="Question input type")
    context_images: Optional[List[UUID]] = Field(None, description="Related image IDs for context")


class AudioQuestionRequest(BaseModel):
    """Audio question request schema (for multipart form data)."""
    context_images: Optional[List[UUID]] = Field(None, description="Related image IDs for context")


class QuestionResponse(BaseModel):
    """Question response schema."""
    id: UUID = Field(..., description="Question unique identifier")
    content: str = Field(..., description="Question text content")
    type: str = Field(..., description="Question input type")
    audio_url: Optional[str] = Field(None, description="Audio file URL (for audio questions)")
    context_images: List[ImageResponse] = Field(default=[], description="Related images for context")
    status: str = Field(..., description="Question processing status")
    created_at: datetime = Field(..., description="Question submission timestamp")
    user_id: UUID = Field(..., description="User who submitted the question")

    class Config:
        from_attributes = True


class QuestionDetailResponse(QuestionResponse):
    """Question detail response with answer."""
    answer: Optional[AnswerResponse] = Field(None, description="Question answer")


class QuestionListResponse(BaseModel):
    """Question list response schema."""
    questions: List[QuestionResponse] = Field(..., description="List of questions")
    pagination: PaginationInfo = Field(..., description="Pagination information")
