"""API schemas."""

from .auth import (
    AppleAuthRequest,
    AuthResponse,
    GoogleAuthRequest,
    MicrosoftAuthRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from .user import UpdateUserProfile, UserProfile
from .image import ImageResponse, ImageUploadResponse
from .question import (
    AudioQuestionRequest,
    QuestionRequest,
    QuestionResponse,
    QuestionDetailResponse,
    QuestionListResponse,
)
from .answer import AnswerResponse
from .common import MessageResponse, PaginationInfo, HealthResponse

__all__ = [
    # Auth
    "AppleAuthRequest",
    "AuthResponse", 
    "GoogleAuthRequest",
    "MicrosoftAuthRequest",
    "RefreshTokenRequest",
    "TokenResponse",
    # User
    "UpdateUserProfile",
    "UserProfile",
    # Image
    "ImageResponse",
    "ImageUploadResponse",
    # Question
    "AudioQuestionRequest",
    "QuestionRequest",
    "QuestionResponse",
    "QuestionDetailResponse",
    "QuestionListResponse",
    # Answer
    "AnswerResponse",
    # Common
    "MessageResponse",
    "PaginationInfo",
    "HealthResponse",
]
