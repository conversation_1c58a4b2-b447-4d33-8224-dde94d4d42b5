"""Answer schemas."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .image import ImageResponse


class AnswerResponse(BaseModel):
    """Answer response schema."""
    id: UUID = Field(..., description="Answer unique identifier")
    content: str = Field(..., description="Answer text content")
    images: List[ImageResponse] = Field(default=[], description="Answer images")
    confidence: Optional[float] = Field(None, description="Answer confidence score")
    sources: List[str] = Field(default=[], description="Information sources used")
    created_at: datetime = Field(..., description="Answer generation timestamp")
    question_id: UUID = Field(..., description="Related question ID")

    class Config:
        from_attributes = True
