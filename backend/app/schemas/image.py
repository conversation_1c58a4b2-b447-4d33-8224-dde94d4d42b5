"""Image schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ImageResponse(BaseModel):
    """Image response schema."""
    id: UUID = Field(..., description="Image unique identifier")
    filename: str = Field(..., description="Original filename")
    url: str = Field(..., description="Image access URL")
    thumbnail_url: Optional[str] = Field(None, description="Thumbnail image URL")
    size: int = Field(..., description="File size in bytes")
    mime_type: str = Field(..., description="Image MIME type")
    width: Optional[int] = Field(None, description="Image width in pixels")
    height: Optional[int] = Field(None, description="Image height in pixels")
    description: Optional[str] = Field(None, description="Image description")
    created_at: datetime = Field(..., description="Upload timestamp")
    user_id: UUID = Field(..., description="Owner user ID")

    class Config:
        from_attributes = True


class ImageUploadResponse(BaseModel):
    """Image upload response schema."""
    image: ImageResponse = Field(..., description="Uploaded image information")
    message: str = Field(..., description="Upload status message")
