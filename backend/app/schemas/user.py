"""User schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class UserProfile(BaseModel):
    """User profile schema."""
    id: UUID = Field(..., description="User unique identifier")
    email: str = Field(..., description="User email address")
    name: Optional[str] = Field(None, description="User full name")
    first_name: Optional[str] = Field(None, description="User first name")
    last_name: Optional[str] = Field(None, description="User last name")
    avatar_url: Optional[str] = Field(None, description="User avatar image URL")
    provider: str = Field(..., description="OAuth provider used for authentication")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last profile update timestamp")

    class Config:
        from_attributes = True


class UpdateUserProfile(BaseModel):
    """Update user profile schema."""
    name: Optional[str] = Field(None, description="User full name")
    first_name: Optional[str] = Field(None, description="User first name")
    last_name: Optional[str] = Field(None, description="User last name")
