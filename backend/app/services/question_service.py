"""Question processing and answer generation service."""

import asyncio
import os
import uuid
from pathlib import Path
from typing import List, Optional

import aiofiles
import speech_recognition as sr
from fastapi import HTTPException, UploadFile, status
from pydub import AudioSegment
from sqlalchemy.orm import Session

from app.config import settings
from app.models import Answer, Image, Question, QuestionStatus, QuestionType, User
from app.services.intelligent_query_service import intelligent_query_service
from app.services.llm_service import llm_service


class QuestionService:
    """Service for handling question processing and answer generation."""
    
    @staticmethod
    def validate_audio_file(file: UploadFile) -> None:
        """Validate uploaded audio file."""
        # Check file size
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Check file extension
        if file.filename:
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in settings.allowed_audio_extensions_list:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Audio type not allowed. Allowed types: {', '.join(settings.allowed_audio_extensions_list)}"
                )
    
    @staticmethod
    async def save_audio_file(
        file_content: bytes,
        filename: str,
        user_id: str
    ) -> tuple[str, str]:
        """Save audio file to disk."""
        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR) / "audio"
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Create user-specific directory
        user_dir = upload_dir / str(user_id)
        user_dir.mkdir(exist_ok=True)
        
        # Generate unique filename
        file_extension = filename.split(".")[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        
        # File path
        audio_path = user_dir / unique_filename
        
        try:
            # Save audio file
            async with aiofiles.open(audio_path, "wb") as f:
                await f.write(file_content)
            
            # Generate URL
            audio_url = f"/uploads/audio/{user_id}/{unique_filename}"
            
            return str(audio_path), audio_url
            
        except Exception as e:
            # Clean up file if something went wrong
            if audio_path.exists():
                audio_path.unlink()
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error saving audio file: {str(e)}"
            )
    
    @staticmethod
    async def transcribe_audio(audio_path: str) -> str:
        """Transcribe audio file to text using speech recognition."""
        try:
            # Convert audio to WAV format for speech recognition
            audio = AudioSegment.from_file(audio_path)
            wav_path = audio_path.replace(audio_path.split(".")[-1], "wav")
            audio.export(wav_path, format="wav")
            
            # Initialize recognizer
            recognizer = sr.Recognizer()
            
            # Transcribe audio
            with sr.AudioFile(wav_path) as source:
                audio_data = recognizer.record(source)
                
                # Use Google Speech Recognition (free tier)
                try:
                    text = recognizer.recognize_google(audio_data)
                    return text
                except sr.UnknownValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Could not understand audio"
                    )
                except sr.RequestError as e:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"Speech recognition service error: {str(e)}"
                    )
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error transcribing audio: {str(e)}"
            )
        finally:
            # Clean up temporary WAV file
            if os.path.exists(wav_path):
                os.remove(wav_path)
    
    @staticmethod
    async def generate_answer(
        question: Question,
        context_images: List[Image],
        db: Session,
        user: User
    ) -> Answer:
        """Generate answer for a question using intelligent query service."""
        try:
            # Use intelligent query service for context-aware answering
            answer = await intelligent_query_service.answer_question_with_context(
                db=db,
                question=question,
                user=user
            )

            # Add context images to answer if relevant and not already processed
            if context_images and not answer.images:
                answer.images = context_images[:3]  # Limit to 3 images
                db.commit()
                db.refresh(answer)

            return answer

        except Exception as e:
            # Update question status to failed
            question.status = QuestionStatus.FAILED
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating answer: {str(e)}"
            )

    @staticmethod
    async def generate_answer_fallback(
        question: Question,
        context_images: List[Image],
        db: Session
    ) -> Answer:
        """Generate answer using fallback method (for backward compatibility)."""
        try:
            # Update question status to processing
            question.status = QuestionStatus.PROCESSING
            db.commit()

            # Generate mock answer based on question content
            answer_content = QuestionService._generate_mock_answer(
                question.content, context_images
            )

            # Create answer
            answer = Answer(
                content=answer_content,
                confidence=0.85,  # Mock confidence score
                sources=["Fallback AI Service", "Knowledge Base"],
                llm_model="fallback",
                question_id=question.id
            )

            # Add context images to answer if relevant
            if context_images:
                answer.images = context_images[:2]  # Limit to 2 images for demo

            db.add(answer)

            # Update question status to completed
            question.status = QuestionStatus.COMPLETED

            db.commit()
            db.refresh(answer)

            return answer

        except Exception as e:
            # Update question status to failed
            question.status = QuestionStatus.FAILED
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating answer: {str(e)}"
            )
    
    @staticmethod
    def _generate_mock_answer(question_content: str, context_images: List[Image]) -> str:
        """Generate a mock answer for demonstration purposes."""
        # Simple keyword-based mock responses
        question_lower = question_content.lower()
        
        if any(word in question_lower for word in ["what", "where", "when", "who", "why", "how"]):
            if context_images:
                return f"Based on the {len(context_images)} image(s) you provided and your question '{question_content}', here's what I can tell you: This appears to be related to the visual content in your images. The images show relevant information that helps answer your question. For a more detailed analysis, I would need to examine the specific visual elements in greater detail."
            else:
                return f"Regarding your question '{question_content}', here's what I can help you with: This is a great question that requires some analysis. Based on the information available, I can provide you with relevant insights and guidance. Would you like me to elaborate on any specific aspect of this topic?"
        
        elif any(word in question_lower for word in ["help", "assist", "support"]):
            return "I'm here to help! I can assist you with various questions and provide information based on text input, audio recordings, and image analysis. Feel free to ask me anything you'd like to know more about."
        
        elif any(word in question_lower for word in ["thank", "thanks"]):
            return "You're welcome! I'm glad I could help. If you have any more questions or need further assistance, please don't hesitate to ask."
        
        else:
            if context_images:
                return f"Thank you for your question and the {len(context_images)} image(s) you shared. I've analyzed your input and can provide relevant information. The visual content helps provide context for a more comprehensive response. Is there anything specific about the images you'd like me to focus on?"
            else:
                return f"I understand your question: '{question_content}'. This is an interesting topic that I can help you explore. Let me provide you with some relevant information and insights that might be helpful for your inquiry."
