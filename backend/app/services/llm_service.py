"""LLM service for AI-powered content processing."""

import asyncio
import base64
import io
import json
import logging
from typing import Dict, List, Optional, Tuple, Union

import openai
import tiktoken
from fastapi import HTTPException, status
from PIL import Image

from app.config import settings

logger = logging.getLogger(__name__)


class LLMService:
    """Service for LLM-powered content processing."""
    
    def __init__(self):
        """Initialize LLM service."""
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.encoding = tiktoken.encoding_for_model("gpt-4")
        
    async def analyze_image(
        self,
        image_content: bytes,
        prompt: Optional[str] = None
    ) -> Dict[str, Union[str, List[str]]]:
        """Analyze image content using GPT-4 Vision."""
        try:
            # Convert image to base64
            image_base64 = base64.b64encode(image_content).decode('utf-8')
            
            # Default prompt for image analysis
            if not prompt:
                prompt = """Analyze this image and provide:
1. A detailed description of what you see
2. Key objects, people, or elements present
3. The setting or context
4. Any text visible in the image
5. Relevant tags or keywords (comma-separated)

Format your response as JSON with keys: description, objects, setting, text_content, tags"""
            
            response = await self.client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            
            # Try to parse as JSON, fallback to structured text
            try:
                result = json.loads(content)
            except json.JSONDecodeError:
                # Fallback: extract information from text response
                result = {
                    "description": content,
                    "objects": [],
                    "setting": "",
                    "text_content": "",
                    "tags": []
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing image: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error analyzing image: {str(e)}"
            )
    
    async def transcribe_audio(
        self,
        audio_content: bytes,
        filename: str
    ) -> str:
        """Transcribe audio using OpenAI Whisper."""
        try:
            # Create a file-like object from bytes
            audio_file = io.BytesIO(audio_content)
            audio_file.name = filename
            
            response = await self.client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                response_format="text"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error transcribing audio: {str(e)}"
            )
    
    async def process_text(
        self,
        text: str,
        task: str = "summarize"
    ) -> Dict[str, Union[str, List[str], float]]:
        """Process text using GPT for various tasks."""
        try:
            prompts = {
                "summarize": f"""Summarize the following text and provide:
1. A concise summary (2-3 sentences)
2. Key points (bullet points)
3. Main topics/tags
4. Sentiment (positive/negative/neutral)

Text: {text}

Format as JSON with keys: summary, key_points, tags, sentiment""",
                
                "analyze": f"""Analyze the following text and provide:
1. Main themes and topics
2. Key entities (people, places, organizations)
3. Important facts or information
4. Emotional tone and sentiment
5. Relevant tags or categories

Text: {text}

Format as JSON with keys: themes, entities, facts, tone, tags""",
                
                "extract": f"""Extract structured information from the following text:
1. Key facts and data points
2. Names, dates, locations mentioned
3. Important concepts or topics
4. Action items or tasks (if any)

Text: {text}

Format as JSON with keys: facts, names_dates_locations, concepts, action_items"""
            }
            
            prompt = prompts.get(task, prompts["summarize"])
            
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that analyzes and processes text content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            
            # Try to parse as JSON
            try:
                result = json.loads(content)
            except json.JSONDecodeError:
                # Fallback structure
                result = {
                    "summary": content,
                    "key_points": [],
                    "tags": [],
                    "sentiment": "neutral"
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing text: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing text: {str(e)}"
            )
    
    async def generate_embeddings(
        self,
        text: str,
        model: str = "text-embedding-3-small"
    ) -> List[float]:
        """Generate embeddings for text using OpenAI."""
        try:
            # Truncate text if too long
            max_tokens = 8000  # Conservative limit
            tokens = self.encoding.encode(text)
            if len(tokens) > max_tokens:
                text = self.encoding.decode(tokens[:max_tokens])
            
            response = await self.client.embeddings.create(
                model=model,
                input=text
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating embeddings: {str(e)}"
            )
    
    async def answer_question(
        self,
        question: str,
        context: List[str],
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> Dict[str, Union[str, float, List[str]]]:
        """Answer a question using provided context."""
        try:
            # Prepare context
            context_text = "\n\n".join(context) if context else ""
            
            # Build conversation history
            messages = [
                {
                    "role": "system",
                    "content": """You are a helpful AI assistant that answers questions based on provided context. 
                    Use the context information to provide accurate, relevant answers. 
                    If the context doesn't contain enough information, say so clearly.
                    Always cite which parts of the context you used in your answer."""
                }
            ]
            
            # Add conversation history if provided
            if conversation_history:
                messages.extend(conversation_history[-5:])  # Keep last 5 exchanges
            
            # Add current question with context
            user_message = f"""Context information:
{context_text}

Question: {question}

Please provide a comprehensive answer based on the context provided."""
            
            messages.append({"role": "user", "content": user_message})
            
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.2
            )
            
            answer_content = response.choices[0].message.content
            
            # Calculate confidence based on context relevance (simplified)
            confidence = 0.8 if context else 0.3
            
            return {
                "answer": answer_content,
                "confidence": confidence,
                "sources": ["AI Analysis", "User Content"],
                "model": "gpt-4"
            }
            
        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error answering question: {str(e)}"
            )
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text."""
        return len(self.encoding.encode(text))
    
    async def batch_process_texts(
        self,
        texts: List[str],
        task: str = "summarize",
        batch_size: int = 5
    ) -> List[Dict]:
        """Process multiple texts in batches."""
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_tasks = [self.process_text(text, task) for text in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in batch processing: {result}")
                    results.append({"error": str(result)})
                else:
                    results.append(result)
        
        return results


# Global instance
llm_service = LLMService()
