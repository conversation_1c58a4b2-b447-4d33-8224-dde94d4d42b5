"""Question model."""

import uuid
from datetime import datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import Column, DateTime, Enum as SQLEnum, ForeignKey, String, Table, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class QuestionType(str, Enum):
    """Question type enumeration."""
    TEXT = "text"
    AUDIO = "audio"
    IMAGE = "image"


class QuestionStatus(str, Enum):
    """Question processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# Association table for question-image context relationship
question_image_context = Table(
    "question_image_context",
    Base.metadata,
    Column(
        "question_id",
        UUID(as_uuid=True),
        ForeignKey("questions.id", ondelete="CASCADE"),
        primary_key=True
    ),
    Column(
        "image_id",
        UUID(as_uuid=True),
        Foreign<PERSON>ey("images.id", ondelete="CASCADE"),
        primary_key=True
    ),
)


class Question(Base):
    """Question model for storing user questions."""
    
    __tablename__ = "questions"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    # Question content
    content = Column(Text, nullable=False)
    type = Column(
        SQLEnum(QuestionType),
        nullable=False,
        default=QuestionType.TEXT
    )
    
    # Audio file information (for audio questions)
    audio_filename = Column(String(255), nullable=True)
    audio_file_path = Column(Text, nullable=True)
    audio_url = Column(Text, nullable=True)
    
    # Processing status
    status = Column(
        SQLEnum(QuestionStatus),
        nullable=False,
        default=QuestionStatus.PENDING,
        index=True
    )

    # LLM processing fields
    llm_processed_content = Column(Text, nullable=True)
    context_summary = Column(Text, nullable=True)
    
    # Foreign key to user
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    # Relationships
    user = relationship("User", back_populates="questions")
    context_images = relationship(
        "Image",
        secondary=question_image_context,
        back_populates="question_contexts"
    )
    answer = relationship(
        "Answer",
        back_populates="question",
        uselist=False,
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Question(id={self.id}, type={self.type}, status={self.status}, user_id={self.user_id})>"
    
    def to_dict(self, include_answer: bool = False) -> dict:
        """Convert question to dictionary."""
        data = {
            "id": str(self.id),
            "content": self.content,
            "type": self.type.value,
            "audio_url": self.audio_url,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "user_id": str(self.user_id),
            "context_images": [img.to_dict() for img in self.context_images] if self.context_images else [],
        }
        
        if include_answer and self.answer:
            data["answer"] = self.answer.to_dict()
        
        return data
