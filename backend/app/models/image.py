"""Image model."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Column, DateTime, Enum as S<PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class ProcessingStatus(str, Enum):
    """Processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class Image(Base):
    """Image model for storing uploaded images."""
    
    __tablename__ = "images"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    # File information
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(Text, nullable=False)
    thumbnail_path = Column(Text, nullable=True)
    url = Column(Text, nullable=False)
    thumbnail_url = Column(Text, nullable=True)
    
    # File metadata
    size = Column(Integer, nullable=False)  # File size in bytes
    mime_type = Column(String(100), nullable=False)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    
    # Optional description
    description = Column(Text, nullable=True)

    # LLM processing fields
    llm_description = Column(Text, nullable=True)
    llm_tags = Column(ARRAY(String), nullable=True)
    processing_status = Column(
        SQLEnum(ProcessingStatus),
        nullable=False,
        default=ProcessingStatus.PENDING
    )
    
    # Foreign key to user
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    # Relationships
    user = relationship("User", back_populates="images")
    question_contexts = relationship(
        "Question",
        secondary="question_image_context",
        back_populates="context_images"
    )
    answer_images = relationship(
        "Answer",
        secondary="answer_images",
        back_populates="images"
    )
    
    def __repr__(self) -> str:
        return f"<Image(id={self.id}, filename={self.filename}, user_id={self.user_id})>"
    
    def to_dict(self) -> dict:
        """Convert image to dictionary."""
        return {
            "id": str(self.id),
            "filename": self.original_filename,
            "url": self.url,
            "thumbnail_url": self.thumbnail_url,
            "size": self.size,
            "mime_type": self.mime_type,
            "width": self.width,
            "height": self.height,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "user_id": str(self.user_id),
        }
