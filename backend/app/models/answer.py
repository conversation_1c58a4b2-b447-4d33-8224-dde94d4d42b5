"""Answer model."""

import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy import Column, DateTime, Float, ForeignKey, String, Table, Text
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


# Association table for answer-image relationship
answer_images = Table(
    "answer_images",
    Base.metadata,
    Column(
        "answer_id",
        UUID(as_uuid=True),
        ForeignKey("answers.id", ondelete="CASCADE"),
        primary_key=True
    ),
    Column(
        "image_id",
        UUID(as_uuid=True),
        ForeignKey("images.id", ondelete="CASCADE"),
        primary_key=True
    ),
)


class Answer(Base):
    """Answer model for storing generated answers."""
    
    __tablename__ = "answers"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    # Answer content
    content = Column(Text, nullable=False)
    
    # Confidence score (0.0 to 1.0)
    confidence = Column(Float, nullable=True)
    
    # Information sources
    sources = Column(ARRAY(String), nullable=True)

    # LLM processing fields
    llm_model = Column(String(100), nullable=True)
    reasoning = Column(Text, nullable=True)
    
    # Foreign key to question
    question_id = Column(
        UUID(as_uuid=True),
        ForeignKey("questions.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True
    )
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    # Relationships
    question = relationship("Question", back_populates="answer")
    images = relationship(
        "Image",
        secondary=answer_images,
        back_populates="answer_images"
    )
    
    def __repr__(self) -> str:
        return f"<Answer(id={self.id}, question_id={self.question_id}, confidence={self.confidence})>"
    
    def to_dict(self) -> dict:
        """Convert answer to dictionary."""
        return {
            "id": str(self.id),
            "content": self.content,
            "confidence": self.confidence,
            "sources": self.sources or [],
            "images": [img.to_dict() for img in self.images] if self.images else [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "question_id": str(self.question_id),
        }
