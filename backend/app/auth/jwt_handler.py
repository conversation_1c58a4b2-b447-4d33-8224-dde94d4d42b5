"""JWT token handling utilities."""

import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional

from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from app.config import settings
from app.models import AuthToken, TokenType, User


class JWTHandler:
    """JWT token handler for authentication."""
    
    @staticmethod
    def create_access_token(
        user_id: str,
        expires_delta: Optional[timedelta] = None
    ) -> Dict[str, str]:
        """Create access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        jti = str(uuid.uuid4())
        to_encode = {
            "sub": str(user_id),
            "exp": expire,
            "jti": jti,
            "type": "access"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        return {
            "token": encoded_jwt,
            "jti": jti,
            "expires_at": expire.isoformat()
        }
    
    @staticmethod
    def create_refresh_token(
        user_id: str,
        expires_delta: Optional[timedelta] = None
    ) -> Dict[str, str]:
        """Create refresh token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS
            )
        
        jti = str(uuid.uuid4())
        to_encode = {
            "sub": str(user_id),
            "exp": expire,
            "jti": jti,
            "type": "refresh"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        return {
            "token": encoded_jwt,
            "jti": jti,
            "expires_at": expire.isoformat()
        }
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            return payload
        except JWTError:
            return None
    
    @staticmethod
    def store_token(
        db: Session,
        user_id: str,
        token_data: Dict[str, str],
        token_type: TokenType
    ) -> AuthToken:
        """Store token in database."""
        auth_token = AuthToken(
            token=token_data["token"],
            token_type=token_type,
            jti=token_data["jti"],
            expires_at=datetime.fromisoformat(token_data["expires_at"]),
            user_id=user_id
        )
        
        db.add(auth_token)
        db.commit()
        db.refresh(auth_token)
        
        return auth_token
    
    @staticmethod
    def revoke_token(db: Session, jti: str) -> bool:
        """Revoke token by JTI."""
        token = db.query(AuthToken).filter(AuthToken.jti == jti).first()
        if token:
            token.revoke()
            db.commit()
            return True
        return False
    
    @staticmethod
    def revoke_all_user_tokens(db: Session, user_id: str) -> int:
        """Revoke all tokens for a user."""
        tokens = db.query(AuthToken).filter(
            AuthToken.user_id == user_id,
            AuthToken.is_revoked == "0"
        ).all()
        
        count = 0
        for token in tokens:
            token.revoke()
            count += 1
        
        db.commit()
        return count
    
    @staticmethod
    def is_token_revoked(db: Session, jti: str) -> bool:
        """Check if token is revoked."""
        token = db.query(AuthToken).filter(AuthToken.jti == jti).first()
        if not token:
            return True  # Token not found, consider it revoked
        
        return token.is_revoked_bool or token.is_expired()
    
    @staticmethod
    def cleanup_expired_tokens(db: Session) -> int:
        """Clean up expired tokens from database."""
        expired_tokens = db.query(AuthToken).filter(
            AuthToken.expires_at < datetime.utcnow()
        ).all()
        
        count = len(expired_tokens)
        for token in expired_tokens:
            db.delete(token)
        
        db.commit()
        return count
