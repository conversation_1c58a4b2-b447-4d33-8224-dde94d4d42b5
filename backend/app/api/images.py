"""Image API routes."""

import math
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile, status
from sqlalchemy.orm import Session

from app.auth import get_current_user
from app.database import get_db
from app.models import Image, User
from app.schemas import ImageResponse, MessageResponse, PaginationInfo
from app.services import ImageService

router = APIRouter(prefix="/images", tags=["Images"])


@router.post("/upload", response_model=ImageResponse, status_code=status.HTTP_201_CREATED)
async def upload_image(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload an image file."""
    # Validate file
    ImageService.validate_image_file(file)
    
    # Read file content
    file_content = await file.read()
    
    # Process image
    compressed_content, thumbnail_content, original_size = await ImageService.process_image(
        file_content, file.filename
    )
    
    # Save files
    image_path, thumbnail_path, image_url, thumbnail_url = await ImageService.save_image_files(
        compressed_content, thumbnail_content, file.filename, str(current_user.id)
    )
    
    # Create database record
    image = Image(
        filename=file.filename.replace(" ", "_"),
        original_filename=file.filename,
        file_path=image_path,
        thumbnail_path=thumbnail_path,
        url=image_url,
        thumbnail_url=thumbnail_url,
        size=len(compressed_content),
        mime_type=file.content_type or "image/jpeg",
        width=original_size[0],
        height=original_size[1],
        description=description,
        user_id=current_user.id
    )
    
    db.add(image)
    db.commit()
    db.refresh(image)
    
    return ImageResponse.from_orm(image)


@router.get("/{image_id}", response_model=ImageResponse)
async def get_image(
    image_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get image by ID."""
    image = db.query(Image).filter(
        Image.id == image_id,
        Image.user_id == current_user.id
    ).first()
    
    if not image:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Image not found"
        )
    
    return ImageResponse.from_orm(image)


@router.delete("/{image_id}", response_model=MessageResponse)
async def delete_image(
    image_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete image by ID."""
    image = db.query(Image).filter(
        Image.id == image_id,
        Image.user_id == current_user.id
    ).first()
    
    if not image:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Image not found"
        )
    
    # Delete files from disk
    await ImageService.delete_image_files(image.file_path, image.thumbnail_path)
    
    # Delete from database
    db.delete(image)
    db.commit()
    
    return MessageResponse(message="Image deleted successfully")


@router.get("/user", response_model=dict)
async def get_user_images(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all images uploaded by the current user."""
    # Calculate offset
    offset = (page - 1) * limit
    
    # Get total count
    total = db.query(Image).filter(Image.user_id == current_user.id).count()
    
    # Get images
    images = db.query(Image).filter(
        Image.user_id == current_user.id
    ).order_by(Image.created_at.desc()).offset(offset).limit(limit).all()
    
    # Calculate pagination info
    pages = math.ceil(total / limit)
    has_next = page < pages
    has_prev = page > 1
    
    pagination = PaginationInfo(
        page=page,
        limit=limit,
        total=total,
        pages=pages,
        has_next=has_next,
        has_prev=has_prev
    )
    
    return {
        "images": [ImageResponse.from_orm(image) for image in images],
        "pagination": pagination
    }
