"""Search API routes for semantic search and content discovery."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.auth import get_current_user
from app.database import get_db
from app.models import ContentType, User
from app.services.intelligent_query_service import intelligent_query_service

router = APIRouter(prefix="/search", tags=["Search"])


class SemanticSearchRequest(BaseModel):
    """Semantic search request schema."""
    query: str = Field(..., description="Search query text")
    content_types: Optional[List[ContentType]] = Field(None, description="Filter by content types")
    limit: int = Field(default=20, ge=1, le=100, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Minimum similarity score")


class SearchResult(BaseModel):
    """Search result schema."""
    content_id: UUID = Field(..., description="Content unique identifier")
    content_type: str = Field(..., description="Type of content")
    content_text: str = Field(..., description="Content text")
    similarity_score: float = Field(..., description="Similarity score")
    metadata: dict = Field(default={}, description="Additional metadata")
    created_at: str = Field(..., description="Content creation timestamp")


class SemanticSearchResponse(BaseModel):
    """Semantic search response schema."""
    query: str = Field(..., description="Original search query")
    results: List[SearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")


class ContentInsightsResponse(BaseModel):
    """Content insights response schema."""
    total_items: int = Field(..., description="Total number of content items")
    insights: str = Field(..., description="AI-generated insights about the content")
    common_themes: List[str] = Field(default=[], description="Common themes found in content")
    sentiment_distribution: dict = Field(default={}, description="Distribution of sentiment scores")
    tag_frequency: dict = Field(default={}, description="Frequency of tags")


@router.post("/semantic", response_model=SemanticSearchResponse)
async def semantic_search(
    request: SemanticSearchRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform semantic search across user's content."""
    try:
        # Perform semantic search
        search_results = await intelligent_query_service.semantic_search(
            db=db,
            query=request.query,
            user_id=current_user.id,
            content_types=request.content_types,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold
        )
        
        # Convert results to response format
        results = []
        for embedding, similarity in search_results:
            result = SearchResult(
                content_id=embedding.content_id,
                content_type=embedding.content_type.value,
                content_text=embedding.content_text,
                similarity_score=similarity,
                metadata=embedding.metadata or {},
                created_at=embedding.created_at.isoformat()
            )
            results.append(result)
        
        return SemanticSearchResponse(
            query=request.query,
            results=results,
            total_results=len(results)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing semantic search: {str(e)}"
        )


@router.get("/insights", response_model=ContentInsightsResponse)
async def get_content_insights(
    content_type: Optional[ContentType] = Query(None, description="Filter by content type"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get AI-powered insights about user's content."""
    try:
        insights = await intelligent_query_service.get_content_insights(
            db=db,
            user_id=current_user.id,
            content_type=content_type
        )
        
        return ContentInsightsResponse(**insights)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting content insights: {str(e)}"
        )


@router.get("/related/{content_id}", response_model=List[SearchResult])
async def get_related_content(
    content_id: UUID,
    content_type: ContentType = Query(..., description="Type of the source content"),
    limit: int = Query(default=5, ge=1, le=20, description="Maximum number of related items"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get content related to a specific item."""
    try:
        related_content = await intelligent_query_service.suggest_related_content(
            db=db,
            content_id=content_id,
            content_type=content_type,
            user_id=current_user.id,
            limit=limit
        )
        
        # Convert results to response format
        results = []
        for embedding, similarity in related_content:
            result = SearchResult(
                content_id=embedding.content_id,
                content_type=embedding.content_type.value,
                content_text=embedding.content_text,
                similarity_score=similarity,
                metadata=embedding.metadata or {},
                created_at=embedding.created_at.isoformat()
            )
            results.append(result)
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting related content: {str(e)}"
        )
