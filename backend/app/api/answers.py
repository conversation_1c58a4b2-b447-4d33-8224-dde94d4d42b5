"""Answer API routes."""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.auth import get_current_user
from app.database import get_db
from app.models import Answer, Question, User
from app.schemas import AnswerResponse

router = APIRouter(prefix="/answers", tags=["Answers"])


@router.get("/{question_id}", response_model=AnswerResponse)
async def get_answer_for_question(
    question_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get answer for a specific question."""
    # First check if question exists and belongs to user
    question = db.query(Question).filter(
        Question.id == question_id,
        Question.user_id == current_user.id
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    # Get answer
    answer = db.query(Answer).filter(Answer.question_id == question_id).first()
    
    if not answer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Answer not found"
        )
    
    return AnswerResponse.from_orm(answer)
