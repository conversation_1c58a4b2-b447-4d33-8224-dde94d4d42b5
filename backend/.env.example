# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/wheresz_db
DATABASE_URL_ASYNC=postgresql+asyncpg://username:password@localhost:5432/wheresz_db

# For development with SQLite (uncomment to use)
# DATABASE_URL=sqlite:///./wheresz.db
# DATABASE_URL_ASYNC=sqlite+aiosqlite:///./wheresz.db

# Supabase Configuration (recommended for production)
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
USE_SUPABASE=true

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OAuth Configuration
# Apple Sign-In
APPLE_CLIENT_ID=your.app.bundle.id
APPLE_TEAM_ID=your_team_id
APPLE_KEY_ID=your_key_id
APPLE_PRIVATE_KEY_PATH=path/to/AuthKey_KEYID.p8

# Google Sign-In
GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Microsoft Sign-In
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
MICROSOFT_TENANT_ID=common

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_AUDIO_EXTENSIONS=mp3,wav,m4a,aac

# Image Processing
IMAGE_QUALITY=85
THUMBNAIL_SIZE=300,300

# Audio Processing
SPEECH_TO_TEXT_SERVICE=openai  # Options: openai, google, azure, aws
GOOGLE_SPEECH_API_KEY=your-google-speech-api-key

# LLM and AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_VISION_MODEL=gpt-4-vision-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_WHISPER_MODEL=whisper-1

# Alternative LLM providers (optional)
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Embedding and Vector Search Configuration
EMBEDDING_DIMENSION=1536  # OpenAI text-embedding-3-small dimension
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_LENGTH=4000
MAX_EMBEDDING_BATCH_SIZE=100

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:19006,exp://localhost:19000

# Application Configuration
APP_NAME=WhereZ API
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# External Services (Optional for MVP)
# AWS S3 Configuration (for production file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=wheresz-uploads

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0
